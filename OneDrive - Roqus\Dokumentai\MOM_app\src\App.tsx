import React, { useState, useCallback, useEffect } from 'react';
import {
  <PERSON>s<PERSON>ist,
  <PERSON>rror<PERSON>ou<PERSON><PERSON>,
  WhisperStatusIndicator,
  TranscriptionManager,
  ProfessionalTranscriptViewer,
  DynamicAudioVisualizer
} from './components';
import { useAudioRecorder, useTranscription } from './hooks';
import { Meeting, TranscriptionStatus, Speaker } from './types/meeting';
import { createDemoMeetings } from './utils/demoData';
import { identifySpeakers } from './services/speakerService';
import { Headphones, Plus, Mic2, TestTube, Zap, Settings, List, Square, Menu, X } from 'lucide-react';
import './styles/background.css';

function App() {
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);
  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);
  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();
  const { 
    transcript, 
    isTranscribing, 
    transcribeAudioEnhanced, 
    cancelTranscription,
    editSegment,
    clearTranscript, 
    clearError,
    currentTranscriptionId,
    progress,
    isWhisperConfigured
  } = useTranscription();

  const handleStartRecording = useCallback(async (title: string) => {
    try {
      await startRecording();
      
      const newMeeting: Meeting = {
        id: Date.now().toString(),
        title: title,
        date: new Date(),
        duration: 0,
        status: 'recording',
        transcriptionStatus: {
          state: 'not_started',
        },
      };
      
      setCurrentMeeting(newMeeting);
      setMeetings(prev => [newMeeting, ...prev]);
      setActiveView('recording');
      clearTranscript();
    } catch (error) {
      console.error('Nepavyko pradėti įrašymo:', error);
      throw error;
    }
  }, [startRecording, clearTranscript]);

  const handleStopRecording = useCallback(async () => {
    try {
      const audioBlob = await stopRecording();
      
      if (currentMeeting && audioBlob) {
        const updatedMeeting: Meeting = {
          ...currentMeeting,
          status: 'completed',
          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),
          audioBlob,
          transcriptionStatus: {
            state: 'not_started',
          },
        };

        setCurrentMeeting(updatedMeeting);
        setMeetings(prev => 
          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)
        );

        // Switch to transcription management view
        setActiveView('transcription');
      }
    } catch (error) {
      console.error('Nepavyko sustabdyti įrašymo:', error);
      alert('Nepavyko sustabdyti įrašymo.');
    }
  }, [stopRecording, currentMeeting]);

  const handleStartTranscription = useCallback(async (meetingId: string) => {
    const meeting = meetings.find(m => m.id === meetingId);
    if (!meeting || !meeting.audioBlob) return;

    // Update meeting status to pending
    const updatedMeeting: Meeting = {
      ...meeting,
      transcriptionStatus: {
        state: 'pending',
        startedAt: new Date(),
      },
    };

    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));
    setSelectedMeetingForTranscript(updatedMeeting);

    try {
      // Start professional transcription
      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {
        onProgress: (progress) => {
          setMeetings(prev => prev.map(m => 
            m.id === meetingId 
              ? { 
                  ...m, 
                  transcriptionStatus: { 
                    ...m.transcriptionStatus, 
                    progress,
                    state: 'processing' 
                  } 
                }
              : m
          ));
        },
        onStatusUpdate: (status) => {
          setMeetings(prev => prev.map(m => 
            m.id === meetingId 
              ? { 
                  ...m, 
                  transcriptionStatus: status 
                }
              : m
          ));
        },
        enhanceSpeakers: true,
      });

      // Update meeting with completed transcription
      const completedMeeting: Meeting = {
        ...updatedMeeting,
        transcript: result.segments,
        participants: result.speakers,
        metadata: result.metadata,
        transcriptionStatus: {
          state: 'completed',
          progress: 100,
          startedAt: updatedMeeting.transcriptionStatus.startedAt,
          completedAt: new Date(),
        },
      };

      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));
      setSelectedMeetingForTranscript(completedMeeting);
      
      // Automatiškai pereiti į rezultatų puslapį
      setActiveView('transcript');

      console.log('✅ Transkribavimas sėkmingai baigtas:', {
        segments: result.segments.length,
        speakers: result.speakers.length,
        words: result.metadata.totalWords,
        confidence: result.metadata.averageConfidence,
      });

    } catch (error: any) {
      console.error('❌ Transkribavimo klaida:', error);
      
      const errorMeeting: Meeting = {
        ...updatedMeeting,
        transcriptionStatus: {
          state: 'failed',
          error: error.message,
          startedAt: updatedMeeting.transcriptionStatus.startedAt,
        },
      };

      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));
    }
  }, [meetings, transcribeAudioEnhanced]);

  const handleCancelTranscription = useCallback((meetingId: string) => {
    cancelTranscription();
    
    setMeetings(prev => prev.map(m => 
      m.id === meetingId 
        ? { 
            ...m, 
            transcriptionStatus: { 
              ...m.transcriptionStatus, 
              state: 'cancelled' as const 
            } 
          }
        : m
    ));
  }, [cancelTranscription]);

  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {
    editSegment(segmentId, newText);
    
    // Update the meeting's transcript
    setMeetings(prev => prev.map(meeting => 
      meeting.id === meetingId
        ? {
            ...meeting,
            transcript: meeting.transcript?.map(segment => 
              segment.id === segmentId 
                ? {
                    ...segment,
                    text: newText,
                    isEdited: true,
                    editedAt: new Date(),
                    editedBy: 'user'
                  }
                : segment
            ),
          }
        : meeting
    ));
  }, [editSegment]);

  const handleSelectMeeting = useCallback((meeting: Meeting) => {
    setCurrentMeeting(meeting);
    if (meeting.transcript && meeting.transcript.length > 0) {
      setSelectedMeetingForTranscript(meeting);
      setActiveView('transcript');
    }
  }, []);

  const handleDeleteMeeting = useCallback((meetingId: string) => {
    setMeetings(prev => prev.filter(m => m.id !== meetingId));
    if (currentMeeting?.id === meetingId) {
      setCurrentMeeting(null);
    }
    if (selectedMeetingForTranscript?.id === meetingId) {
      setSelectedMeetingForTranscript(null);
    }
  }, [currentMeeting, selectedMeetingForTranscript]);

  const handleExportMeeting = useCallback((meeting: Meeting) => {
    const exportData = {
      title: meeting.title,
      date: meeting.date.toISOString(),
      duration: meeting.duration,
      transcript: meeting.transcript || transcript,
      participants: meeting.participants || [],
      metadata: meeting.metadata || {},
      transcriptionStatus: meeting.transcriptionStatus,
    };

    const dataStr = JSON.stringify(exportData, null, 2);
    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);
    
    const exportFileDefaultName = `meeting-${meeting.title.replace(/\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportFileDefaultName);
    linkElement.click();
  }, [transcript]);



  const loadDemoData = useCallback(() => {
    const demoMeetings = createDemoMeetings().map(meeting => ({
      ...meeting,
      transcriptionStatus: {
        state: 'completed' as const,
        progress: 100,
        completedAt: meeting.date,
      },
    }));
    setMeetings(demoMeetings);
    setActiveView('transcript');
  }, []);

  // Close mobile menu when clicking outside or on escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (isMobileMenuOpen && !target.closest('.mobile-menu-container')) {
        setIsMobileMenuOpen(false);
      }
    };

    const handleEscape = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMobileMenuOpen) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when mobile menu is open
      document.body.classList.add('mobile-menu-open');
    } else {
      document.body.classList.remove('mobile-menu-open');
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscape);
      document.body.classList.remove('mobile-menu-open');
    };
  }, [isMobileMenuOpen]);

  return (
    <ErrorBoundary>
      <div className="min-h-screen elegant-background font-inter">
        {/* Elegant Grid Pattern */}
        <div className="elegant-grid"></div>

        {/* Sophisticated Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-40 -right-40 w-80 h-80 background-accent-1 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 background-accent-2 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 background-accent-3 rounded-full blur-3xl"></div>
        </div>

        {/* Content Wrapper */}
        <div className="relative z-10">
          {/* Glassmorphic Header */}
          <header className="fixed top-0 left-0 right-0 z-50 glassmorphic-header">
            <div className="max-w-7xl mx-auto px-4 sm:px-6">
              <div className="flex items-center justify-between h-16">
                {/* Logo - Always visible */}
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20">
                    <Mic2 className="h-5 w-5 text-white" />
                  </div>
                  <div>
                    <h1 className="text-white font-semibold text-lg bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent">
                      MOM App
                    </h1>
                    <p className="text-white/60 text-xs font-medium hidden sm:block">Meeting Recording & Transcription</p>
                  </div>
                </div>

                {/* Desktop Navigation - Hidden on mobile */}
                <div className="hidden md:flex items-center gap-4">
                  <div className="bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg">
                    <WhisperStatusIndicator />
                  </div>
                  <div className="flex bg-white/10 backdrop-blur-sm rounded-xl p-1 border border-white/20 shadow-lg">
                    <button
                      onClick={() => setActiveView('recording')}
                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                        activeView === 'recording'
                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      Įrašymas
                    </button>
                    <button
                      onClick={() => setActiveView('transcription')}
                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                        activeView === 'transcription'
                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      Transkribavimas
                    </button>
                    <button
                      onClick={() => setActiveView('transcript')}
                      className={`px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 ${
                        activeView === 'transcript'
                          ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                          : 'text-white/70 hover:text-white hover:bg-white/10'
                      }`}
                    >
                      Rezultatai
                    </button>
                  </div>
                </div>

                {/* Desktop Action Buttons - Hidden on mobile */}
                <div className="hidden md:flex items-center gap-3">
                  {meetings.length === 0 && (
                    <button
                      onClick={loadDemoData}
                      className="px-4 py-2 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg"
                    >
                      <TestTube className="h-4 w-4 mr-2 inline" />
                      Demo
                    </button>
                  )}
                  <button
                    onClick={() => {
                      setActiveView('recording');
                      if (!recordingState.isRecording) {
                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);
                      }
                    }}
                    className="px-4 py-2 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20"
                  >
                    <Plus className="h-4 w-4" />
                    Naujas pokalbis
                  </button>
                </div>

                {/* Mobile menu button - Only visible on mobile */}
                <div className="md:hidden flex items-center">
                  <button
                    onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                    className="p-2 rounded-lg bg-white/10 hover:bg-white/20 border border-white/20 shadow-lg transition-all duration-300"
                    aria-expanded={isMobileMenuOpen}
                  >
                    <span className="sr-only">Open main menu</span>
                    {isMobileMenuOpen ? (
                      <X className="h-6 w-6 text-white" />
                    ) : (
                      <Menu className="h-6 w-6 text-white" />
                    )}
                  </button>
                </div>
              </div>
            </div>

            {/* Mobile menu, show/hide based on menu state */}
            <div className={`md:hidden ${isMobileMenuOpen ? 'block' : 'hidden'}`}>
              <div className="px-4 pt-2 pb-4 space-y-3 glassmorphic-card m-2 rounded-xl border border-white/20 shadow-lg mobile-menu-container">
                {/* Mobile Navigation Pills */}
                <div className="flex flex-col space-y-2">
                  <button
                    onClick={() => {
                      setActiveView('recording');
                      setIsMobileMenuOpen(false);
                    }}
                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${
                      activeView === 'recording'
                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'
                    }`}
                  >
                    <Mic2 className="h-4 w-4 mr-2" />
                    Įrašymas
                  </button>
                  <button
                    onClick={() => {
                      setActiveView('transcription');
                      setIsMobileMenuOpen(false);
                    }}
                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${
                      activeView === 'transcription'
                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'
                    }`}
                  >
                    <Zap className="h-4 w-4 mr-2" />
                    Transkribavimas
                  </button>
                  <button
                    onClick={() => {
                      setActiveView('transcript');
                      setIsMobileMenuOpen(false);
                    }}
                    className={`px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center ${
                      activeView === 'transcript'
                        ? 'bg-gradient-to-r from-blue-500/80 to-purple-600/80 text-white shadow-lg border border-white/20'
                        : 'text-white/70 hover:text-white hover:bg-white/10 border border-white/10'
                    }`}
                  >
                    <Headphones className="h-4 w-4 mr-2" />
                    Rezultatai
                  </button>
                </div>

                {/* Mobile Action Buttons */}
                <div className="pt-2 border-t border-white/10">
                  <div className="flex flex-col space-y-2">
                    {meetings.length === 0 && (
                      <button
                        onClick={() => {
                          loadDemoData();
                          setIsMobileMenuOpen(false);
                        }}
                        className="px-4 py-3 text-sm font-medium text-white/80 hover:text-white bg-white/10 hover:bg-white/20 backdrop-blur-sm rounded-lg transition-all duration-300 border border-white/20 shadow-lg flex items-center"
                      >
                        <TestTube className="h-4 w-4 mr-2" />
                        Demo
                      </button>
                    )}
                    <button
                      onClick={() => {
                        setActiveView('recording');
                        setIsMobileMenuOpen(false);
                        if (!recordingState.isRecording) {
                          handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);
                        }
                      }}
                      className="px-4 py-3 text-sm font-medium text-white bg-gradient-to-r from-blue-500/80 to-purple-600/80 hover:from-blue-600/90 hover:to-purple-700/90 backdrop-blur-sm rounded-lg transition-all duration-300 flex items-center gap-2 shadow-lg border border-white/20"
                    >
                      <Plus className="h-4 w-4" />
                      Naujas pokalbis
                    </button>

                    {/* Whisper Status in Mobile Menu */}
                    <div className="px-4 py-3 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20 shadow-lg">
                      <div className="flex items-center">
                        <span className="text-white/70 text-sm mr-2">Whisper Status:</span>
                        <WhisperStatusIndicator />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </header>





        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 py-8 pt-24 md:pt-24">
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 md:gap-8 min-h-[calc(100vh-140px)]">

            {/* Main Content Area */}
            <div className="lg:col-span-3">
              {/* Recording View */}
              {activeView === 'recording' && (
                <div className="glassmorphic-card rounded-2xl h-full">
                  {/* Header */}
                  <div className="p-5 border-b border-white/15">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20">
                        <Mic2 className="h-5 w-5 text-white" />
                      </div>
                      <div>
                        <h2 className="text-base font-semibold text-white">Pokalbio įrašymas</h2>
                        <p className="text-xs text-white/50 font-medium">Pradėkite naują pokalbio įrašymą</p>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-8 flex-1 flex flex-col justify-center items-center min-h-[500px]">
                    {!recordingState.isRecording ? (
                      <div className="max-w-lg mx-auto text-center space-y-10 animate-fade-in">
                        {/* Hero Icon */}
                        <div className="relative">
                          <div className="w-32 h-32 bg-gradient-to-br from-blue-500/20 via-indigo-500/15 to-purple-600/20 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-white/10 shadow-2xl mx-auto">
                            <div className="w-20 h-20 bg-gradient-to-br from-blue-500/30 to-purple-600/30 rounded-2xl flex items-center justify-center">
                              <Mic2 className="h-10 w-10 text-white" />
                            </div>
                          </div>
                          <div className="absolute -inset-4 bg-gradient-to-r from-blue-500/10 via-transparent to-purple-600/10 rounded-full blur-xl opacity-60" />
                        </div>

                        {/* Content */}
                        <div className="space-y-6">
                          <div className="space-y-4">
                            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight">
                              Pradėkite naują pokalbį
                            </h2>
                            <p className="text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto">
                              Profesionalus garso įrašymas su automatine transkribavimo technologija
                            </p>
                          </div>

                          {/* Features */}
                          <div className="grid grid-cols-1 sm:grid-cols-3 gap-6 mt-8">
                            <div className="text-center space-y-3 group">
                              <div className="w-14 h-14 bg-gradient-to-br from-blue-500/20 to-blue-600/20 rounded-xl flex items-center justify-center mx-auto border border-blue-400/20 group-hover:scale-110 transition-transform duration-300">
                                <Zap className="h-6 w-6 text-blue-400" />
                              </div>
                              <p className="text-sm text-white/70 font-medium leading-tight">Automatinis<br />transkribavimas</p>
                            </div>
                            <div className="text-center space-y-3 group">
                              <div className="w-14 h-14 bg-gradient-to-br from-purple-500/20 to-purple-600/20 rounded-xl flex items-center justify-center mx-auto border border-purple-400/20 group-hover:scale-110 transition-transform duration-300">
                                <Headphones className="h-6 w-6 text-purple-400" />
                              </div>
                              <p className="text-sm text-white/70 font-medium leading-tight">Aukšta garso<br />kokybė</p>
                            </div>
                            <div className="text-center space-y-3 group">
                              <div className="w-14 h-14 bg-gradient-to-br from-indigo-500/20 to-indigo-600/20 rounded-xl flex items-center justify-center mx-auto border border-indigo-400/20 group-hover:scale-110 transition-transform duration-300">
                                <Settings className="h-6 w-6 text-indigo-400" />
                              </div>
                              <p className="text-sm text-white/70 font-medium leading-tight">Pažangūs<br />nustatymai</p>
                            </div>
                          </div>
                        </div>

                        {/* CTA Button */}
                        <button
                          onClick={() => handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}
                          className="group relative px-10 py-4 bg-gradient-to-r from-blue-600 via-blue-700 to-indigo-700 hover:from-blue-500 hover:via-blue-600 hover:to-indigo-600 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-blue-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-indigo-400/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <Plus className="h-5 w-5 transition-transform duration-200 group-hover:rotate-90" />
                          <span>Pradėti įrašymą</span>
                        </button>
                      </div>
                    ) : (
                      <div className="max-w-lg mx-auto text-center space-y-10 animate-fade-in">
                        {/* Recording Status */}
                        <div className="relative">
                          <div className="w-32 h-32 bg-gradient-to-br from-red-500/30 via-red-600/20 to-red-700/30 backdrop-blur-xl rounded-3xl flex items-center justify-center border border-red-400/20 shadow-2xl mx-auto animate-pulse">
                            <div className="w-20 h-20 bg-gradient-to-br from-red-500/40 to-red-600/40 rounded-2xl flex items-center justify-center">
                              <Mic2 className="h-10 w-10 text-white animate-bounce" />
                            </div>
                          </div>
                          <div className="absolute -inset-4 bg-gradient-to-r from-red-500/20 via-transparent to-red-600/20 rounded-full blur-xl opacity-80 animate-pulse" />

                          {/* Recording indicator */}
                          <div className="absolute -top-2 -right-2">
                            <div className="relative">
                              <div className="w-6 h-6 bg-red-500 rounded-full animate-pulse" />
                              <div className="absolute inset-0 w-6 h-6 bg-red-500/50 rounded-full animate-ping" />
                            </div>
                          </div>
                        </div>

                        {/* Content */}
                        <div className="space-y-6">
                          <div className="space-y-4">
                            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white leading-tight tracking-tight">
                              Pokalbis įrašomas
                            </h2>
                            <p className="text-base sm:text-lg text-white/70 leading-relaxed font-medium max-w-2xl mx-auto">
                              Jūsų pokalbis sėkmingai įrašomas ir bus automatiškai transkribuojamas
                            </p>
                          </div>

                          {/* Recording Stats */}
                          <div className="bg-white/5 backdrop-blur-md rounded-2xl p-6 border border-white/10">
                            <div className="grid grid-cols-2 gap-6">
                              <div className="text-center">
                                <div className="text-2xl font-bold text-white mb-1">
                                  {Math.floor(recordingState.duration / 60)}:{String(recordingState.duration % 60).padStart(2, '0')}
                                </div>
                                <div className="text-sm text-white/60 font-medium">Trukmė</div>
                              </div>
                              <div className="text-center">
                                <div className="flex items-center justify-center space-x-1 mb-1">
                                  <DynamicAudioVisualizer recordingState={recordingState} />
                                </div>
                                <div className="text-sm text-white/60 font-medium">Garso lygis</div>
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Stop Button */}
                        <button
                          onClick={handleStopRecording}
                          className="group relative px-10 py-4 bg-gradient-to-r from-red-600 via-red-700 to-red-800 hover:from-red-500 hover:via-red-600 hover:to-red-700 text-white rounded-2xl font-semibold transition-all duration-300 flex items-center gap-3 shadow-2xl border border-red-400/20 backdrop-blur-sm transform hover:scale-[1.02] active:scale-[0.98] mx-auto"
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-red-400/20 to-red-500/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                          <Square className="h-5 w-5" />
                          <span>Sustabdyti įrašymą</span>
                        </button>
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* Transcription View */}
              {activeView === 'transcription' && (
                <div className="glassmorphic-card rounded-2xl h-full">
                  {/* Header */}
                  <div className="p-6 border-b border-white/20">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20">
                        <Zap className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-semibold text-white">Transkribavimas</h2>
                        <p className="text-sm text-white/60">Audio failų konvertavimas į tekstą naudojant AI</p>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <TranscriptionManager
                      meetings={meetings}
                      onStartTranscription={handleStartTranscription}
                      onCancelTranscription={handleCancelTranscription}
                      isTranscribing={isTranscribing}
                      currentTranscriptionId={currentTranscriptionId}
                      onDeleteMeeting={handleDeleteMeeting}
                      onViewResults={() => setActiveView('transcript')}
                    />
                  </div>
                </div>
              )}

              {/* Transcript View */}
              {activeView === 'transcript' && (
                <div className="glassmorphic-card rounded-2xl h-full">
                  {/* Header */}
                  <div className="p-6 border-b border-white/20">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 backdrop-blur-sm rounded-xl flex items-center justify-center shadow-lg border border-white/20">
                        <Headphones className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <h2 className="text-xl font-semibold text-white">Rezultatai</h2>
                        <p className="text-sm text-white/60">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>
                      </div>
                    </div>
                  </div>

                  {/* Content */}
                  <div className="p-6">
                    <ProfessionalTranscriptViewer
                      meetings={meetings}
                      onDeleteMeeting={handleDeleteMeeting}
                      onGoToTranscription={() => setActiveView('transcription')}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Sidebar - Meetings List */}
            <div className="lg:col-span-1">
              <div className="glassmorphic-card rounded-2xl h-full">
                {/* Sidebar Header */}
                <div className="p-5 border-b border-white/15">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 backdrop-blur-sm rounded-lg flex items-center justify-center shadow-lg border border-white/20">
                      <List className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <h2 className="text-base font-semibold text-white">Pokalbiai</h2>
                      <p className="text-xs text-white/50 font-medium">Visi pokalbiai ({meetings.length})</p>
                    </div>
                  </div>
                </div>

                {/* Sidebar Content */}
                <div className="p-5">
                  {meetings.length === 0 ? (
                    <div className="text-center py-10">
                      <div className="w-14 h-14 bg-white/8 backdrop-blur-sm rounded-full flex items-center justify-center mx-auto mb-3 border border-white/15 shadow-lg">
                        <List className="h-5 w-5 text-white/50" />
                      </div>
                      <h3 className="text-white font-medium mb-1.5 text-sm">Nėra pokalbių</h3>
                      <p className="text-white/50 text-xs leading-relaxed">
                        Pradėkite naują pokalbį, kad pamatytumėte jį čia
                      </p>
                    </div>
                  ) : (
                    <MeetingsList
                      meetings={meetings}
                      currentMeeting={currentMeeting}
                      onSelectMeeting={handleSelectMeeting}
                      onDeleteMeeting={handleDeleteMeeting}
                      onExportMeeting={() => {}}
                      activeView="list"
                    />
                  )}
                </div>
              </div>
            </div>
          </div>
        </main>
        </div> {/* Close content wrapper */}
      </div>
    </ErrorBoundary>
  );
}

export default App; 