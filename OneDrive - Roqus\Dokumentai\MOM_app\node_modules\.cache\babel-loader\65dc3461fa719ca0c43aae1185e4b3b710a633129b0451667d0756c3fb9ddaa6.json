{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, ProfessionalTranscriptViewer, RecordingPanel, GridControls } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap, List } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n\n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `min-h-screen bg-app-gradient font-inter relative overflow-hidden ${activeView === 'recording' ? 'grid-page-recording' : activeView === 'transcription' ? 'grid-page-transcription' : activeView === 'transcript' ? 'grid-page-results' : 'grid-page-home'}`,\n      style: {\n        '--grid-size': `${gridSize}px`,\n        '--grid-rotation': `${gridRotation}deg`,\n        '--grid-color': gridColor\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"fixed top-0 left-0 right-0 z-50\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"elegant-navbar-glass border-b border-white/5\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between h-16\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center gap-3\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"elegant-logo-container group\",\n                      \"data-tooltip\": \"MOM - Meeting Recording App\",\n                      children: /*#__PURE__*/_jsxDEV(Mic2, {\n                        className: \"h-5 w-5 text-white group-hover:text-blue-200 transition-colors duration-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 315,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 311,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"hidden sm:block\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                        className: \"text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                        children: \"MOM App\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 318,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-xs text-white/60 font-medium tracking-wide\",\n                        children: \"Meeting Recording & Transcription\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 321,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 317,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"sm:hidden\",\n                      children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                        className: \"text-lg font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",\n                        children: \"MOM\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 326,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 325,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 310,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 309,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"elegant-status-container\",\n                    children: /*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n                    className: \"elegant-nav-container\",\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"elegant-nav-pills\",\n                      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setActiveView('recording'),\n                        className: `elegant-nav-pill ${activeView === 'recording' ? 'active' : ''}`,\n                        \"data-tooltip\": \"\\u012Era\\u0161yti nauj\\u0105 pokalb\\u012F\",\n                        children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 350,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"hidden md:inline font-medium\",\n                          children: \"\\u012Era\\u0161ymas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 351,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 343,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setActiveView('transcription'),\n                        className: `elegant-nav-pill ${activeView === 'transcription' ? 'active' : ''}`,\n                        \"data-tooltip\": \"Transkribuoti audio failus\",\n                        children: [/*#__PURE__*/_jsxDEV(Zap, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 360,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"hidden md:inline font-medium\",\n                          children: \"Transkribavimas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 361,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 353,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => setActiveView('transcript'),\n                        className: `elegant-nav-pill ${activeView === 'transcript' ? 'active' : ''}`,\n                        \"data-tooltip\": \"Per\\u017Ei\\u016Br\\u0117ti rezultatus ir transkriptus\",\n                        children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 370,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          className: \"hidden md:inline font-medium\",\n                          children: \"Rezultatai\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 371,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 363,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 342,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 341,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 334,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-3\",\n                  children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: loadDemoData,\n                    className: \"elegant-demo-button group\",\n                    \"data-tooltip\": \"U\\u017Ekrauti demonstracinius duomenis testavimui\",\n                    children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                      className: \"h-4 w-4 group-hover:rotate-12 transition-transform duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 385,\n                      columnNumber: 23\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden lg:inline font-medium\",\n                      children: \"Demo\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 386,\n                      columnNumber: 23\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 380,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                    onClick: () => {\n                      setActiveView('recording');\n                      if (!recordingState.isRecording) {\n                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                      }\n                    },\n                    className: \"elegant-primary-button group\",\n                    \"data-tooltip\": \"Prad\\u0117ti nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\",\n                    children: [/*#__PURE__*/_jsxDEV(Plus, {\n                      className: \"h-4 w-4 group-hover:rotate-90 transition-transform duration-300\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 401,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"hidden lg:inline font-semibold\",\n                      children: \"Naujas pokalbis\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 402,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 391,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 378,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 306,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 305,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 304,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 303,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"pt-20 px-4 sm:px-6 lg:px-8 pb-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"max-w-7xl mx-auto\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"grid grid-cols-1 xl:grid-cols-4 gap-6 min-h-[calc(100vh-140px)]\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"xl:col-span-3\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"relative h-full\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `absolute inset-0 transition-all duration-300 ease-out ${activeView === 'recording' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card h-full flex flex-col\",\n                      style: {\n                        padding: 'var(--space-6)',\n                        background: 'var(--color-bg-elevated)',\n                        borderRadius: 'var(--radius-xl)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center justify-between mb-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"flex items-center gap-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",\n                            style: {\n                              background: 'linear-gradient(135deg, var(--color-accent-primary), var(--color-accent-hover))'\n                            },\n                            children: /*#__PURE__*/_jsxDEV(Mic2, {\n                              className: \"h-6 w-6 text-white\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 439,\n                              columnNumber: 29\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 436,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                              className: \"text-2xl font-semibold text-primary\",\n                              children: \"Pokalbio \\u012Fra\\u0161ymas\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 442,\n                              columnNumber: 29\n                            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                              className: \"text-sm text-secondary\",\n                              children: \"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105 arba t\\u0119skite esam\\u0105\"\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 443,\n                              columnNumber: 29\n                            }, this)]\n                          }, void 0, true, {\n                            fileName: _jsxFileName,\n                            lineNumber: 441,\n                            columnNumber: 27\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 435,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                          onClick: () => {\n                            if (!recordingState.isRecording) {\n                              handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                            }\n                          },\n                          className: \"tooltip btn btn-primary hidden sm:flex\",\n                          \"data-tooltip\": \"Prad\\u0117ti nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\",\n                          children: [/*#__PURE__*/_jsxDEV(Plus, {\n                            className: \"h-4 w-4\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 455,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                            children: \"Naujas pokalbis\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 456,\n                            columnNumber: 27\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 446,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 434,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/_jsxDEV(RecordingPanel, {\n                          recordingState: recordingState,\n                          currentMeeting: currentMeeting,\n                          onStartRecording: handleStartRecording,\n                          onStopRecording: handleStopRecording,\n                          onPauseRecording: () => {},\n                          onResumeRecording: () => {}\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 462,\n                          columnNumber: 25\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 461,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 428,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 423,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `absolute inset-0 transition-all duration-300 ease-out ${activeView === 'transcription' ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4 pointer-events-none'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card h-full flex flex-col\",\n                      style: {\n                        padding: 'var(--space-6)',\n                        background: 'var(--color-bg-elevated)',\n                        borderRadius: 'var(--radius-xl)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",\n                          style: {\n                            background: 'linear-gradient(135deg, #8b5cf6, #a855f7)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Zap, {\n                            className: \"h-6 w-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 490,\n                            columnNumber: 25\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 487,\n                          columnNumber: 23\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                            className: \"text-2xl font-semibold text-primary\",\n                            children: \"Transkribavimas\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 493,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-secondary\",\n                            children: \"Audio fail\\u0173 konvertavimas \\u012F tekst\\u0105 naudojant AI\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 494,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 492,\n                          columnNumber: 23\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 486,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                          meetings: meetings,\n                          onStartTranscription: handleStartTranscription,\n                          onCancelTranscription: handleCancelTranscription,\n                          isTranscribing: isTranscribing,\n                          currentTranscriptionId: currentTranscriptionId,\n                          onDeleteMeeting: handleDeleteMeeting,\n                          onViewResults: () => setActiveView('transcript')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 500,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 499,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 480,\n                      columnNumber: 19\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 475,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: `absolute inset-0 transition-all duration-300 ease-out ${activeView === 'transcript' ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4 pointer-events-none'}`,\n                    children: /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"card h-full flex flex-col\",\n                      style: {\n                        padding: 'var(--space-6)',\n                        background: 'var(--color-bg-elevated)',\n                        borderRadius: 'var(--radius-xl)'\n                      },\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center gap-4 mb-6\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",\n                          style: {\n                            background: 'linear-gradient(135deg, #10b981, #059669)'\n                          },\n                          children: /*#__PURE__*/_jsxDEV(Headphones, {\n                            className: \"h-6 w-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 529,\n                            columnNumber: 25\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 526,\n                          columnNumber: 23\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                            className: \"text-2xl font-semibold text-primary\",\n                            children: \"Rezultatai\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 532,\n                            columnNumber: 25\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm text-secondary\",\n                            children: \"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 533,\n                            columnNumber: 25\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 531,\n                          columnNumber: 23\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 525,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex-1 flex flex-col\",\n                        children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n                          meetings: meetings,\n                          onDeleteMeeting: handleDeleteMeeting,\n                          onGoToTranscription: () => setActiveView('transcription')\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 539,\n                          columnNumber: 23\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 538,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 519,\n                      columnNumber: 19\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 514,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 421,\n                  columnNumber: 17\n                }, this), \" \"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 420,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"xl:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"card h-full flex flex-col\",\n                style: {\n                  padding: 'var(--space-6)',\n                  background: 'var(--color-bg-elevated)',\n                  borderRadius: 'var(--radius-xl)'\n                },\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center gap-4 mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 rounded-lg flex items-center justify-center shadow-md\",\n                    style: {\n                      background: 'linear-gradient(135deg, #6366f1, #8b5cf6)'\n                    },\n                    children: /*#__PURE__*/_jsxDEV(List, {\n                      className: \"h-5 w-5 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 563,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 560,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-lg font-semibold text-primary\",\n                      children: \"Pokalbiai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 566,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-tertiary\",\n                      children: [\"Visi j\\u016Bs\\u0173 pokalbiai (\", meetings.length, \")\"]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 567,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 565,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 559,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 flex flex-col\",\n                  children: /*#__PURE__*/_jsxDEV(MeetingsList, {\n                    meetings: meetings,\n                    currentMeeting: currentMeeting,\n                    onSelectMeeting: handleSelectMeeting,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onExportMeeting: () => {},\n                    activeView: \"list\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 573,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 572,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 553,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 552,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 416,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(GridControls, {\n            onGridSizeChange: setGridSize,\n            onGridRotationChange: setGridRotation,\n            onGridColorChange: setGridColor,\n            currentSize: gridSize,\n            currentRotation: gridRotation,\n            currentColor: gridColor\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 587,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 415,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), \" \"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"QZGo74Iv2G+R2QYYb+sHjp3F0Ks=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "RecordingPanel", "GridControls", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "List", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "gridSize", "setGridSize", "gridRotation", "setGridRotation", "gridColor", "setGridColor", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "children", "className", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isRecording", "toLocaleString", "padding", "background", "borderRadius", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "onGridSizeChange", "onGridRotationChange", "onGridColorChange", "currentSize", "currentRotation", "currentColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { \n  Recording<PERSON>utton, \n  RecordingIndicator, \n  TranscriptViewer, \n  MeetingsList, \n  ErrorBoundary, \n  WhisperConfig,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  RecordingPanel,\n  CollapsibleTranscriptsList,\n  GridControls\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List } from 'lucide-react';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  \n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <div\n        className={`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${\n          activeView === 'recording' ? 'grid-page-recording' :\n          activeView === 'transcription' ? 'grid-page-transcription' :\n          activeView === 'transcript' ? 'grid-page-results' :\n          'grid-page-home'\n        }`}\n        style={{\n          '--grid-size': `${gridSize}px`,\n          '--grid-rotation': `${gridRotation}deg`,\n          '--grid-color': gridColor,\n        } as React.CSSProperties}\n      >\n        {/* Content wrapper */}\n        <div className=\"relative z-10\">\n          {/* Elegant Modern Header */}\n        <header className=\"fixed top-0 left-0 right-0 z-50\">\n          <div className=\"elegant-navbar-glass border-b border-white/5\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex items-center justify-between h-16\">\n\n                {/* Logo and Brand Section */}\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div\n                      className=\"elegant-logo-container group\"\n                      data-tooltip=\"MOM - Meeting Recording App\"\n                    >\n                      <Mic2 className=\"h-5 w-5 text-white group-hover:text-blue-200 transition-colors duration-300\" />\n                    </div>\n                    <div className=\"hidden sm:block\">\n                      <h1 className=\"text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                        MOM App\n                      </h1>\n                      <p className=\"text-xs text-white/60 font-medium tracking-wide\">\n                        Meeting Recording & Transcription\n                      </p>\n                    </div>\n                    <div className=\"sm:hidden\">\n                      <h1 className=\"text-lg font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                        MOM\n                      </h1>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Center Navigation Section */}\n                <div className=\"flex items-center gap-6\">\n                  {/* Whisper Status with enhanced styling */}\n                  <div className=\"elegant-status-container\">\n                    <WhisperStatusIndicator />\n                  </div>\n\n                  {/* Elegant Navigation Pills */}\n                  <nav className=\"elegant-nav-container\">\n                    <div className=\"elegant-nav-pills\">\n                      <button\n                        onClick={() => setActiveView('recording')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'recording' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Įrašyti naują pokalbį\"\n                      >\n                        <Mic2 className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Įrašymas</span>\n                      </button>\n                      <button\n                        onClick={() => setActiveView('transcription')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'transcription' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Transkribuoti audio failus\"\n                      >\n                        <Zap className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Transkribavimas</span>\n                      </button>\n                      <button\n                        onClick={() => setActiveView('transcript')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'transcript' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Peržiūrėti rezultatus ir transkriptus\"\n                      >\n                        <Headphones className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Rezultatai</span>\n                      </button>\n                    </div>\n                  </nav>\n                </div>\n\n                {/* Action Buttons Section */}\n                <div className=\"flex items-center gap-3\">\n                  {meetings.length === 0 && (\n                    <button\n                      onClick={loadDemoData}\n                      className=\"elegant-demo-button group\"\n                      data-tooltip=\"Užkrauti demonstracinius duomenis testavimui\"\n                    >\n                      <TestTube className=\"h-4 w-4 group-hover:rotate-12 transition-transform duration-300\" />\n                      <span className=\"hidden lg:inline font-medium\">Demo</span>\n                    </button>\n                  )}\n\n                  {/* Primary CTA Button */}\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      if (!recordingState.isRecording) {\n                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                      }\n                    }}\n                    className=\"elegant-primary-button group\"\n                    data-tooltip=\"Pradėti naują pokalbio įrašymą\"\n                  >\n                    <Plus className=\"h-4 w-4 group-hover:rotate-90 transition-transform duration-300\" />\n                    <span className=\"hidden lg:inline font-semibold\">Naujas pokalbis</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n\n\n        {/* Modern Main Content */}\n        <main className=\"pt-20 px-4 sm:px-6 lg:px-8 pb-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6 min-h-[calc(100vh-140px)]\">\n\n              {/* Main Content Area */}\n              <div className=\"xl:col-span-3\">\n                <div className=\"relative h-full\">\n                  {/* Recording View */}\n                  <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                    activeView === 'recording'\n                      ? 'opacity-100 translate-x-0'\n                      : 'opacity-0 -translate-x-4 pointer-events-none'\n                  }`}>\n                    <div className=\"card h-full flex flex-col\" style={{\n                      padding: 'var(--space-6)',\n                      background: 'var(--color-bg-elevated)',\n                      borderRadius: 'var(--radius-xl)'\n                    }}>\n                      {/* Header */}\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center gap-4\">\n                          <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                            background: 'linear-gradient(135deg, var(--color-accent-primary), var(--color-accent-hover))'\n                          }}>\n                            <Mic2 className=\"h-6 w-6 text-white\" />\n                          </div>\n                          <div>\n                            <h2 className=\"text-2xl font-semibold text-primary\">Pokalbio įrašymas</h2>\n                            <p className=\"text-sm text-secondary\">Pradėkite naują pokalbio įrašymą arba tęskite esamą</p>\n                          </div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            if (!recordingState.isRecording) {\n                              handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                            }\n                          }}\n                          className=\"tooltip btn btn-primary hidden sm:flex\"\n                          data-tooltip=\"Pradėti naują pokalbio įrašymą\"\n                        >\n                          <Plus className=\"h-4 w-4\" />\n                          <span>Naujas pokalbis</span>\n                        </button>\n                      </div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 flex flex-col\">\n                        <RecordingPanel\n                          recordingState={recordingState}\n                          currentMeeting={currentMeeting}\n                          onStartRecording={handleStartRecording}\n                          onStopRecording={handleStopRecording}\n                        onPauseRecording={() => {}}\n                        onResumeRecording={() => {}}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcription View */}\n                <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                  activeView === 'transcription'\n                    ? 'opacity-100 translate-x-0'\n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"card h-full flex flex-col\" style={{\n                    padding: 'var(--space-6)',\n                    background: 'var(--color-bg-elevated)',\n                    borderRadius: 'var(--radius-xl)'\n                  }}>\n                    {/* Header */}\n                    <div className=\"flex items-center gap-4 mb-6\">\n                      <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                        background: 'linear-gradient(135deg, #8b5cf6, #a855f7)'\n                      }}>\n                        <Zap className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-2xl font-semibold text-primary\">Transkribavimas</h2>\n                        <p className=\"text-sm text-secondary\">Audio failų konvertavimas į tekstą naudojant AI</p>\n                      </div>\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 flex flex-col\">\n                      <TranscriptionManager\n                        meetings={meetings}\n                        onStartTranscription={handleStartTranscription}\n                        onCancelTranscription={handleCancelTranscription}\n                        isTranscribing={isTranscribing}\n                        currentTranscriptionId={currentTranscriptionId}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onViewResults={() => setActiveView('transcript')}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcript View */}\n                <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                  activeView === 'transcript'\n                    ? 'opacity-100 translate-x-0'\n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"card h-full flex flex-col\" style={{\n                    padding: 'var(--space-6)',\n                    background: 'var(--color-bg-elevated)',\n                    borderRadius: 'var(--radius-xl)'\n                  }}>\n                    {/* Header */}\n                    <div className=\"flex items-center gap-4 mb-6\">\n                      <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                        background: 'linear-gradient(135deg, #10b981, #059669)'\n                      }}>\n                        <Headphones className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-2xl font-semibold text-primary\">Rezultatai</h2>\n                        <p className=\"text-sm text-secondary\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 flex flex-col\">\n                      <ProfessionalTranscriptViewer\n                        meetings={meetings}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onGoToTranscription={() => setActiveView('transcription')}\n                      />\n                    </div>\n                  </div>\n                </div>\n                </div> {/* Close relative h-full div */}\n              </div>\n            </div>\n\n            {/* Sidebar - Meetings List */}\n            <div className=\"xl:col-span-1\">\n              <div className=\"card h-full flex flex-col\" style={{\n                padding: 'var(--space-6)',\n                background: 'var(--color-bg-elevated)',\n                borderRadius: 'var(--radius-xl)'\n              }}>\n                {/* Sidebar Header */}\n                <div className=\"flex items-center gap-4 mb-6\">\n                  <div className=\"w-10 h-10 rounded-lg flex items-center justify-center shadow-md\" style={{\n                    background: 'linear-gradient(135deg, #6366f1, #8b5cf6)'\n                  }}>\n                    <List className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-lg font-semibold text-primary\">Pokalbiai</h2>\n                    <p className=\"text-xs text-tertiary\">Visi jūsų pokalbiai ({meetings.length})</p>\n                  </div>\n                </div>\n\n                {/* Sidebar Content */}\n                <div className=\"flex-1 flex flex-col\">\n                  <MeetingsList\n                    meetings={meetings}\n                    currentMeeting={currentMeeting}\n                    onSelectMeeting={handleSelectMeeting}\n                    onDeleteMeeting={handleDeleteMeeting}\n                    onExportMeeting={() => {}}\n                    activeView=\"list\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Grid Controls */}\n          <GridControls\n            onGridSizeChange={setGridSize}\n            onGridRotationChange={setGridRotation}\n            onGridColorChange={setGridColor}\n            currentSize={gridSize}\n            currentRotation={gridRotation}\n            currentColor={gridColor}\n          />\n        </main>\n        </div> {/* Close content wrapper div */}\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAIEC,YAAY,EACZC,aAAa,EAEbC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,cAAc,EAEdC,YAAY,QACP,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAYC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC0B,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG3B,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAA+C,WAAW,CAAC;;EAEvG;EACA,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,GAAG,CAAC;EAC7C,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,0BAA0B,CAAC;EAEtE,MAAM;IAAEoC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAG/B,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJgC,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAGxC,gBAAgB,CAAC,CAAC;EAEtB,MAAMyC,oBAAoB,GAAGlD,WAAW,CAAC,MAAOmD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDrC,iBAAiB,CAAC4B,UAAU,CAAC;MAC7B9B,WAAW,CAACwC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1ClC,aAAa,CAAC,WAAW,CAAC;MAC1BiB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,mBAAmB,GAAGjE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMkE,SAAS,GAAG,MAAM7B,aAAa,CAAC,CAAC;MAEvC,IAAId,cAAc,IAAI2C,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAG5C,cAAc;UACjBoC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEU,IAAI,CAACC,KAAK,CAAC,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhC,cAAc,CAACkC,IAAI,CAACa,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTN,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDrC,iBAAiB,CAAC2C,cAAc,CAAC;QACjC7C,WAAW,CAACwC,IAAI,IACdA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAK9B,cAAc,CAAC8B,EAAE,GAAGc,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACA5C,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDU,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACpC,aAAa,EAAEd,cAAc,CAAC,CAAC;EAEnC,MAAMmD,wBAAwB,GAAG1E,WAAW,CAAC,MAAO2E,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAGvD,QAAQ,CAACwD,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBiB,SAAS,EAAE,IAAIxB,IAAI,CAAC;MACtB;IACF,CAAC;IAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3E9C,+BAA+B,CAACyC,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMrC,uBAAuB,CAACkC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGhC,QAAQ,IAAK;UACxB1B,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAE;cACnB,GAAGY,CAAC,CAACZ,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDW,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAGtB,MAAM,IAAK;UAC1BrC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAED;UACvB,CAAC,GACDa,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB3B,UAAU,EAAEuC,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB3B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACb8B,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAAS;UACvDU,WAAW,EAAE,IAAIlC,IAAI,CAAC;QACxB;MACF,CAAC;MAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7E9C,+BAA+B,CAACyD,gBAAgB,CAAC;;MAEjD;MACAvD,aAAa,CAAC,YAAY,CAAC;MAE3BoC,OAAO,CAACyB,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMgC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBP,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACiC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB;QAChD;MACF,CAAC;MAEDxD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACnD,QAAQ,EAAEqB,uBAAuB,CAAC,CAAC;EAEvC,MAAMuD,yBAAyB,GAAGjG,WAAW,CAAE2E,SAAiB,IAAK;IACnEhC,mBAAmB,CAAC,CAAC;IAErBrB,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;MACE,GAAGH,CAAC;MACJZ,mBAAmB,EAAE;QACnB,GAAGY,CAAC,CAACZ,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDW,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,mBAAmB,CAAC,CAAC;EAEzB,MAAMuD,iBAAiB,GAAGlG,WAAW,CAAC,CAAC2E,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/FxD,WAAW,CAACuD,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACA9E,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAACvB,EAAE,KAAKsB,SAAS,GACpB;QACE,GAAGC,OAAO;QACVpC,UAAU,GAAA6D,mBAAA,GAAEzB,OAAO,CAACpC,UAAU,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACjD,EAAE,KAAK8C,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAInD,IAAI,CAAC,CAAC;UACpBoD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAM+D,mBAAmB,GAAG3G,WAAW,CAAE4E,OAAgB,IAAK;IAC5DpD,iBAAiB,CAACoD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACpC,UAAU,IAAIoC,OAAO,CAACpC,UAAU,CAACkD,MAAM,GAAG,CAAC,EAAE;MACvDhE,+BAA+B,CAACkD,OAAO,CAAC;MACxChD,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgF,mBAAmB,GAAG5G,WAAW,CAAE2E,SAAiB,IAAK;IAC7DrD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAAC+C,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC,CAAC;IACzD,IAAI,CAAApD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,EAAE,MAAKsB,SAAS,EAAE;MACpCnD,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAE4B,EAAE,MAAKsB,SAAS,EAAE;MAClDjD,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMqF,mBAAmB,GAAG9G,WAAW,CAAE4E,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjB5D,KAAK,EAAEyB,OAAO,CAACzB,KAAK;MACpBM,IAAI,EAAEmB,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC;MAChCtD,QAAQ,EAAEkB,OAAO,CAAClB,QAAQ;MAC1BlB,UAAU,EAAEoC,OAAO,CAACpC,UAAU,IAAIA,UAAU;MAC5C6C,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC3B,mBAAmB,EAAEgB,OAAO,CAAChB;IAC/B,CAAC;IAED,MAAMqD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAACzB,KAAK,CAACoE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAACtF,UAAU,CAAC,CAAC;EAIhB,MAAMuF,YAAY,GAAG/H,WAAW,CAAC,MAAM;IACrC,MAAMgI,YAAY,GAAGtH,kBAAkB,CAAC,CAAC,CAAC6D,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACbwC,WAAW,EAAEZ,OAAO,CAACnB;MACvB;IACF,CAAC,CAAC,CAAC;IACHnC,WAAW,CAAC0G,YAAY,CAAC;IACzBpG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEV,OAAA,CAAChB,aAAa;IAAA+H,QAAA,eACZ/G,OAAA;MACEgH,SAAS,EAAE,oEACTvG,UAAU,KAAK,WAAW,GAAG,qBAAqB,GAClDA,UAAU,KAAK,eAAe,GAAG,yBAAyB,GAC1DA,UAAU,KAAK,YAAY,GAAG,mBAAmB,GACjD,gBAAgB,EACf;MACHwG,KAAK,EAAE;QACL,aAAa,EAAE,GAAGtG,QAAQ,IAAI;QAC9B,iBAAiB,EAAE,GAAGE,YAAY,KAAK;QACvC,cAAc,EAAEE;MAClB,CAAyB;MAAAgG,QAAA,gBAGzB/G,OAAA;QAAKgH,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE9B/G,OAAA;UAAQgH,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eACjD/G,OAAA;YAAKgH,SAAS,EAAC,8CAA8C;YAAAD,QAAA,eAC3D/G,OAAA;cAAKgH,SAAS,EAAC,wCAAwC;cAAAD,QAAA,eACrD/G,OAAA;gBAAKgH,SAAS,EAAC,wCAAwC;gBAAAD,QAAA,gBAGrD/G,OAAA;kBAAKgH,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,eACtC/G,OAAA;oBAAKgH,SAAS,EAAC,yBAAyB;oBAAAD,QAAA,gBACtC/G,OAAA;sBACEgH,SAAS,EAAC,8BAA8B;sBACxC,gBAAa,6BAA6B;sBAAAD,QAAA,eAE1C/G,OAAA,CAACL,IAAI;wBAACqH,SAAS,EAAC;sBAA6E;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC7F,CAAC,eACNrH,OAAA;sBAAKgH,SAAS,EAAC,iBAAiB;sBAAAD,QAAA,gBAC9B/G,OAAA;wBAAIgH,SAAS,EAAC,yFAAyF;wBAAAD,QAAA,EAAC;sBAExG;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACLrH,OAAA;wBAAGgH,SAAS,EAAC,iDAAiD;wBAAAD,QAAA,EAAC;sBAE/D;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAG,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACD,CAAC,eACNrH,OAAA;sBAAKgH,SAAS,EAAC,WAAW;sBAAAD,QAAA,eACxB/G,OAAA;wBAAIgH,SAAS,EAAC,yFAAyF;wBAAAD,QAAA,EAAC;sBAExG;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,gBAEtC/G,OAAA;oBAAKgH,SAAS,EAAC,0BAA0B;oBAAAD,QAAA,eACvC/G,OAAA,CAACf,sBAAsB;sBAAAiI,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvB,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAC,uBAAuB;oBAAAD,QAAA,eACpC/G,OAAA;sBAAKgH,SAAS,EAAC,mBAAmB;sBAAAD,QAAA,gBAChC/G,OAAA;wBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,WAAW,CAAE;wBAC1CsG,SAAS,EAAE,oBACTvG,UAAU,KAAK,WAAW,GAAG,QAAQ,GAAG,EAAE,EACzC;wBACH,gBAAa,2CAAuB;wBAAAsG,QAAA,gBAEpC/G,OAAA,CAACL,IAAI;0BAACqH,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5BrH,OAAA;0BAAMgH,SAAS,EAAC,8BAA8B;0BAAAD,QAAA,EAAC;wBAAQ;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC,eACTrH,OAAA;wBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,eAAe,CAAE;wBAC9CsG,SAAS,EAAE,oBACTvG,UAAU,KAAK,eAAe,GAAG,QAAQ,GAAG,EAAE,EAC7C;wBACH,gBAAa,4BAA4B;wBAAAsG,QAAA,gBAEzC/G,OAAA,CAACH,GAAG;0BAACmH,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC3BrH,OAAA;0BAAMgH,SAAS,EAAC,8BAA8B;0BAAAD,QAAA,EAAC;wBAAe;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/D,CAAC,eACTrH,OAAA;wBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,YAAY,CAAE;wBAC3CsG,SAAS,EAAE,oBACTvG,UAAU,KAAK,YAAY,GAAG,QAAQ,GAAG,EAAE,EAC1C;wBACH,gBAAa,sDAAuC;wBAAAsG,QAAA,gBAEpD/G,OAAA,CAACP,UAAU;0BAACuH,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAClCrH,OAAA;0BAAMgH,SAAS,EAAC,8BAA8B;0BAAAD,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC1D,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAC,yBAAyB;kBAAAD,QAAA,GACrC5G,QAAQ,CAACqE,MAAM,KAAK,CAAC,iBACpBxE,OAAA;oBACEsH,OAAO,EAAET,YAAa;oBACtBG,SAAS,EAAC,2BAA2B;oBACrC,gBAAa,mDAA8C;oBAAAD,QAAA,gBAE3D/G,OAAA,CAACJ,QAAQ;sBAACoH,SAAS,EAAC;oBAAiE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACxFrH,OAAA;sBAAMgH,SAAS,EAAC,8BAA8B;sBAAAD,QAAA,EAAC;oBAAI;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpD,CACT,eAGDrH,OAAA;oBACEsH,OAAO,EAAEA,CAAA,KAAM;sBACb5G,aAAa,CAAC,WAAW,CAAC;sBAC1B,IAAI,CAACO,cAAc,CAACsG,WAAW,EAAE;wBAC/BvF,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAACoF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;sBACxE;oBACF,CAAE;oBACFR,SAAS,EAAC,8BAA8B;oBACxC,gBAAa,yDAAgC;oBAAAD,QAAA,gBAE7C/G,OAAA,CAACN,IAAI;sBAACsH,SAAS,EAAC;oBAAiE;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE,CAAC,eACpFrH,OAAA;sBAAMgH,SAAS,EAAC,gCAAgC;sBAAAD,QAAA,EAAC;oBAAe;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACN,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eAOTrH,OAAA;UAAMgH,SAAS,EAAC,iCAAiC;UAAAD,QAAA,gBAC/C/G,OAAA;YAAKgH,SAAS,EAAC,mBAAmB;YAAAD,QAAA,gBAChC/G,OAAA;cAAKgH,SAAS,EAAC,iEAAiE;cAAAD,QAAA,eAG9E/G,OAAA;gBAAKgH,SAAS,EAAC,eAAe;gBAAAD,QAAA,gBAC5B/G,OAAA;kBAAKgH,SAAS,EAAC,iBAAiB;kBAAAD,QAAA,gBAE9B/G,OAAA;oBAAKgH,SAAS,EAAE,yDACdvG,UAAU,KAAK,WAAW,GACtB,2BAA2B,GAC3B,8CAA8C,EACjD;oBAAAsG,QAAA,eACD/G,OAAA;sBAAKgH,SAAS,EAAC,2BAA2B;sBAACC,KAAK,EAAE;wBAChDQ,OAAO,EAAE,gBAAgB;wBACzBC,UAAU,EAAE,0BAA0B;wBACtCC,YAAY,EAAE;sBAChB,CAAE;sBAAAZ,QAAA,gBAEA/G,OAAA;wBAAKgH,SAAS,EAAC,wCAAwC;wBAAAD,QAAA,gBACrD/G,OAAA;0BAAKgH,SAAS,EAAC,yBAAyB;0BAAAD,QAAA,gBACtC/G,OAAA;4BAAKgH,SAAS,EAAC,iEAAiE;4BAACC,KAAK,EAAE;8BACtFS,UAAU,EAAE;4BACd,CAAE;4BAAAX,QAAA,eACA/G,OAAA,CAACL,IAAI;8BAACqH,SAAS,EAAC;4BAAoB;8BAAAE,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACpC,CAAC,eACNrH,OAAA;4BAAA+G,QAAA,gBACE/G,OAAA;8BAAIgH,SAAS,EAAC,qCAAqC;8BAAAD,QAAA,EAAC;4BAAiB;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAI,CAAC,eAC1ErH,OAAA;8BAAGgH,SAAS,EAAC,wBAAwB;8BAAAD,QAAA,EAAC;4BAAmD;8BAAAG,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAG,CAAC;0BAAA;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAC1F,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,eACNrH,OAAA;0BACEsH,OAAO,EAAEA,CAAA,KAAM;4BACb,IAAI,CAACrG,cAAc,CAACsG,WAAW,EAAE;8BAC/BvF,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAACoF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;4BACxE;0BACF,CAAE;0BACFR,SAAS,EAAC,wCAAwC;0BAClD,gBAAa,yDAAgC;0BAAAD,QAAA,gBAE7C/G,OAAA,CAACN,IAAI;4BAACsH,SAAS,EAAC;0BAAS;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC5BrH,OAAA;4BAAA+G,QAAA,EAAM;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAM,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtB,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACN,CAAC,eAGNrH,OAAA;wBAAKgH,SAAS,EAAC,sBAAsB;wBAAAD,QAAA,eACnC/G,OAAA,CAACZ,cAAc;0BACb6B,cAAc,EAAEA,cAAe;0BAC/BZ,cAAc,EAAEA,cAAe;0BAC/BuH,gBAAgB,EAAE5F,oBAAqB;0BACvC6F,eAAe,EAAE9E,mBAAoB;0BACvC+E,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAE;0BAC3BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC;wBAAE;0BAAAb,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC7B;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAE,yDACdvG,UAAU,KAAK,eAAe,GAC1B,2BAA2B,GAC3B,6CAA6C,EAChD;oBAAAsG,QAAA,eACD/G,OAAA;sBAAKgH,SAAS,EAAC,2BAA2B;sBAACC,KAAK,EAAE;wBAChDQ,OAAO,EAAE,gBAAgB;wBACzBC,UAAU,EAAE,0BAA0B;wBACtCC,YAAY,EAAE;sBAChB,CAAE;sBAAAZ,QAAA,gBAEA/G,OAAA;wBAAKgH,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,gBAC3C/G,OAAA;0BAAKgH,SAAS,EAAC,iEAAiE;0BAACC,KAAK,EAAE;4BACtFS,UAAU,EAAE;0BACd,CAAE;0BAAAX,QAAA,eACA/G,OAAA,CAACH,GAAG;4BAACmH,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnC,CAAC,eACNrH,OAAA;0BAAA+G,QAAA,gBACE/G,OAAA;4BAAIgH,SAAS,EAAC,qCAAqC;4BAAAD,QAAA,EAAC;0BAAe;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACxErH,OAAA;4BAAGgH,SAAS,EAAC,wBAAwB;4BAAAD,QAAA,EAAC;0BAA+C;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACtF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNrH,OAAA;wBAAKgH,SAAS,EAAC,sBAAsB;wBAAAD,QAAA,eACnC/G,OAAA,CAACd,oBAAoB;0BACnBiB,QAAQ,EAAEA,QAAS;0BACnB6H,oBAAoB,EAAExE,wBAAyB;0BAC/CyE,qBAAqB,EAAElD,yBAA0B;0BACjDxD,cAAc,EAAEA,cAAe;0BAC/BM,sBAAsB,EAAEA,sBAAuB;0BAC/CqG,eAAe,EAAExC,mBAAoB;0BACrCyC,aAAa,EAAEA,CAAA,KAAMzH,aAAa,CAAC,YAAY;wBAAE;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClD;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAE,yDACdvG,UAAU,KAAK,YAAY,GACvB,2BAA2B,GAC3B,6CAA6C,EAChD;oBAAAsG,QAAA,eACD/G,OAAA;sBAAKgH,SAAS,EAAC,2BAA2B;sBAACC,KAAK,EAAE;wBAChDQ,OAAO,EAAE,gBAAgB;wBACzBC,UAAU,EAAE,0BAA0B;wBACtCC,YAAY,EAAE;sBAChB,CAAE;sBAAAZ,QAAA,gBAEA/G,OAAA;wBAAKgH,SAAS,EAAC,8BAA8B;wBAAAD,QAAA,gBAC3C/G,OAAA;0BAAKgH,SAAS,EAAC,iEAAiE;0BAACC,KAAK,EAAE;4BACtFS,UAAU,EAAE;0BACd,CAAE;0BAAAX,QAAA,eACA/G,OAAA,CAACP,UAAU;4BAACuH,SAAS,EAAC;0BAAoB;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC1C,CAAC,eACNrH,OAAA;0BAAA+G,QAAA,gBACE/G,OAAA;4BAAIgH,SAAS,EAAC,qCAAqC;4BAAAD,QAAA,EAAC;0BAAU;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eACnErH,OAAA;4BAAGgH,SAAS,EAAC,wBAAwB;4BAAAD,QAAA,EAAC;0BAAqD;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5F,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eAGNrH,OAAA;wBAAKgH,SAAS,EAAC,sBAAsB;wBAAAD,QAAA,eACnC/G,OAAA,CAACb,4BAA4B;0BAC3BgB,QAAQ,EAAEA,QAAS;0BACnB+H,eAAe,EAAExC,mBAAoB;0BACrC0C,mBAAmB,EAAEA,CAAA,KAAM1H,aAAa,CAAC,eAAe;wBAAE;0BAAAwG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC3D;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACD,CAAC,KAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrH,OAAA;cAAKgH,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B/G,OAAA;gBAAKgH,SAAS,EAAC,2BAA2B;gBAACC,KAAK,EAAE;kBAChDQ,OAAO,EAAE,gBAAgB;kBACzBC,UAAU,EAAE,0BAA0B;kBACtCC,YAAY,EAAE;gBAChB,CAAE;gBAAAZ,QAAA,gBAEA/G,OAAA;kBAAKgH,SAAS,EAAC,8BAA8B;kBAAAD,QAAA,gBAC3C/G,OAAA;oBAAKgH,SAAS,EAAC,iEAAiE;oBAACC,KAAK,EAAE;sBACtFS,UAAU,EAAE;oBACd,CAAE;oBAAAX,QAAA,eACA/G,OAAA,CAACF,IAAI;sBAACkH,SAAS,EAAC;oBAAoB;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpC,CAAC,eACNrH,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAIgH,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACjErH,OAAA;sBAAGgH,SAAS,EAAC,uBAAuB;sBAAAD,QAAA,GAAC,iCAAqB,EAAC5G,QAAQ,CAACqE,MAAM,EAAC,GAAC;oBAAA;sBAAA0C,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC7E,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnC/G,OAAA,CAACjB,YAAY;oBACXoB,QAAQ,EAAEA,QAAS;oBACnBE,cAAc,EAAEA,cAAe;oBAC/BgI,eAAe,EAAE5C,mBAAoB;oBACrCyC,eAAe,EAAExC,mBAAoB;oBACrC4C,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;oBAC1B7H,UAAU,EAAC;kBAAM;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNrH,OAAA,CAACX,YAAY;YACXkJ,gBAAgB,EAAE3H,WAAY;YAC9B4H,oBAAoB,EAAE1H,eAAgB;YACtC2H,iBAAiB,EAAEzH,YAAa;YAChC0H,WAAW,EAAE/H,QAAS;YACtBgI,eAAe,EAAE9H,YAAa;YAC9B+H,YAAY,EAAE7H;UAAU;YAAAmG,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,KAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB;AAACnH,EAAA,CAlkBQD,GAAG;EAAA,QAWiFX,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAAsJ,EAAA,GAvBb5I,GAAG;AAokBZ,eAAeA,GAAG;AAAC,IAAA4I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}