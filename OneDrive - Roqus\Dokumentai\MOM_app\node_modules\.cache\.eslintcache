[{"C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\index.ts": "3", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\index.ts": "4", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useTranscription.ts": "5", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useAudioRecorder.ts": "6", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingButton.tsx": "7", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingIndicator.tsx": "8", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptViewer.tsx": "9", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\MeetingsList.tsx": "10", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ErrorBoundary.tsx": "11", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\AudioPlayer.tsx": "12", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\utils\\demoData.ts": "13", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\whisperService.ts": "14", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\config\\whisper.ts": "15", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperConfig.tsx": "16", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptionManager.tsx": "17", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ProfessionalTranscriptViewer.tsx": "18", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\speakerService.ts": "19", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingPanel.tsx": "20", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\CollapsibleTranscriptsList.tsx": "21", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperStatusIndicator.tsx": "22", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\GridControls.tsx": "23", "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\DynamicAudioVisualizer.tsx": "24"}, {"size": 274, "mtime": 1753213560441, "results": "25", "hashOfConfig": "26"}, {"size": 30105, "mtime": 1753213564065, "results": "27", "hashOfConfig": "26"}, {"size": 111, "mtime": 1753213559758, "results": "28", "hashOfConfig": "26"}, {"size": 829, "mtime": 1753214225652, "results": "29", "hashOfConfig": "26"}, {"size": 10587, "mtime": 1753213562201, "results": "30", "hashOfConfig": "26"}, {"size": 5762, "mtime": 1753214163617, "results": "31", "hashOfConfig": "26"}, {"size": 2713, "mtime": 1753213562449, "results": "32", "hashOfConfig": "26"}, {"size": 3461, "mtime": 1753213563514, "results": "33", "hashOfConfig": "26"}, {"size": 7791, "mtime": 1753213561850, "results": "34", "hashOfConfig": "26"}, {"size": 6991, "mtime": 1753213563691, "results": "35", "hashOfConfig": "26"}, {"size": 4087, "mtime": 1753213562531, "results": "36", "hashOfConfig": "26"}, {"size": 8068, "mtime": 1753213562597, "results": "37", "hashOfConfig": "26"}, {"size": 4195, "mtime": 1753213562651, "results": "38", "hashOfConfig": "26"}, {"size": 10086, "mtime": 1753213562089, "results": "39", "hashOfConfig": "26"}, {"size": 1243, "mtime": 1753213562027, "results": "40", "hashOfConfig": "26"}, {"size": 4783, "mtime": 1753213562265, "results": "41", "hashOfConfig": "26"}, {"size": 10492, "mtime": 1753213562800, "results": "42", "hashOfConfig": "26"}, {"size": 6801, "mtime": 1753213563862, "results": "43", "hashOfConfig": "26"}, {"size": 9878, "mtime": 1753213562381, "results": "44", "hashOfConfig": "26"}, {"size": 4121, "mtime": 1753213563780, "results": "45", "hashOfConfig": "26"}, {"size": 19278, "mtime": 1753213562926, "results": "46", "hashOfConfig": "26"}, {"size": 866, "mtime": 1753040855623, "results": "47", "hashOfConfig": "26"}, {"size": 6510, "mtime": 1753213562862, "results": "48", "hashOfConfig": "26"}, {"size": 2651, "mtime": 1753214196829, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "scgkdl", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useTranscription.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\hooks\\useAudioRecorder.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\MeetingsList.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\AudioPlayer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\utils\\demoData.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\whisperService.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\config\\whisper.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperConfig.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\TranscriptionManager.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\ProfessionalTranscriptViewer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\services\\speakerService.ts", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\RecordingPanel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\CollapsibleTranscriptsList.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\WhisperStatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\GridControls.tsx", [], [], "C:\\Users\\<USER>\\OneDrive - Roqus\\Dokumentai\\MOM_app\\src\\components\\DynamicAudioVisualizer.tsx", [], []]