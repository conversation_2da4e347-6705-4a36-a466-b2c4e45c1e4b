{"ast": null, "code": "import React from'react';import{Trash2,Headphones,Zap}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const SPEAKER_COLORS=['bg-blue-100 text-blue-800 border-blue-200','bg-green-100 text-green-800 border-green-200','bg-purple-100 text-purple-800 border-purple-200','bg-orange-100 text-orange-800 border-orange-200','bg-pink-100 text-pink-800 border-pink-200','bg-indigo-100 text-indigo-800 border-indigo-200'];export const ProfessionalTranscriptViewer=_ref=>{let{meetings,onDeleteMeeting,onGoToTranscription}=_ref;const completedMeetings=meetings.filter(m=>m.transcriptionStatus.state==='completed'&&m.transcript);const formatTime=seconds=>{const mins=Math.floor(seconds/60);const secs=Math.floor(seconds%60);return`${mins}:${secs.toString().padStart(2,'0')}`;};const formatDuration=seconds=>{const mins=Math.floor(seconds/60);const hrs=Math.floor(mins/60);const remainingMins=mins%60;if(hrs>0){return`${hrs}:${remainingMins.toString().padStart(2,'0')}:${(seconds%60).toString().padStart(2,'0')}`;}return`${mins}:${(seconds%60).toString().padStart(2,'0')}`;};return/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col animate-fade-in\",children:completedMeetings.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-8 overflow-y-auto\",children:completedMeetings.map((meeting,meetingIndex)=>/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 animate-fade-in-up\",style:{animationDelay:`${meetingIndex*100}ms`},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl sm:text-2xl font-bold text-white transition-colors duration-200 mb-2\",children:meeting.title}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-base sm:text-lg text-white/70 transition-colors duration-200\",children:[meeting.date.toLocaleString('lt-LT'),\" \\u2022 \",formatDuration(meeting.duration),\"s\"]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>onDeleteMeeting(meeting.id),className:\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\",title:\"I\\u0161trinti pokalb\\u012F\",children:/*#__PURE__*/_jsx(Trash2,{className:\"h-5 w-5\"})})]}),meeting.transcript&&/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4\",children:meeting.transcript.map((segment,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.01] animate-fade-in-up\",style:{animationDelay:`${meetingIndex*100+index*20}ms`},children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-4\",children:[meeting.participants&&segment.speaker&&/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-full flex items-center justify-center text-white text-sm font-medium transition-all duration-200 hover:scale-110\",children:segment.speaker})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-2\",children:[meeting.participants&&segment.speaker&&/*#__PURE__*/_jsx(\"span\",{className:\"text-base font-semibold text-blue-400 transition-colors duration-200\",children:segment.speaker}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm text-white/50 transition-colors duration-200\",children:[formatTime(segment.timestamp),\" - \",formatTime(segment.endTimestamp||segment.timestamp)]})]}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base sm:text-lg text-white/90 leading-relaxed transition-colors duration-200\",children:segment.text})]})]})},index))})]},meeting.id))}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center animate-pulse\",children:/*#__PURE__*/_jsx(Headphones,{className:\"h-10 w-10 text-green-400\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl sm:text-2xl font-semibold text-white\",children:\"N\\u0117ra transkribuot\\u0173 pokalbi\\u0173\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base sm:text-lg text-white/70 mb-6\",children:\"Transkribuokite pokalb\\u012F, kad pamatytum\\u0117te rezultatus \\u010Dia\"}),onGoToTranscription&&/*#__PURE__*/_jsxs(\"button\",{onClick:onGoToTranscription,className:\"inline-flex items-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-purple-500/80 via-purple-600/70 to-pink-600/80 hover:from-purple-500/90 hover:via-purple-600/80 hover:to-pink-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-purple-400/40 hover:border-purple-300/50 transform hover:scale-105\",children:[/*#__PURE__*/_jsx(Zap,{className:\"h-5 w-5\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Eiti \\u012F transkribavim\\u0105\"})]})]})]})});};", "map": {"version": 3, "names": ["React", "Trash2", "Headphones", "Zap", "jsx", "_jsx", "jsxs", "_jsxs", "SPEAKER_COLORS", "ProfessionalTranscriptViewer", "_ref", "meetings", "onDeleteMeeting", "onGoToTranscription", "completedMeetings", "filter", "m", "transcriptionStatus", "state", "transcript", "formatTime", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "formatDuration", "hrs", "remainingMins", "className", "children", "length", "map", "meeting", "meetingIndex", "style", "animationDelay", "title", "date", "toLocaleString", "duration", "onClick", "id", "segment", "index", "participants", "speaker", "timestamp", "endTimestamp", "text"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/ProfessionalTranscriptViewer.tsx"], "sourcesContent": ["import React, { useState, useMemo } from 'react';\r\nimport { Edit3, Download, Copy, Clock, Users, BarChart3, FileText, Loader2, CheckCircle, Calendar, Mic, Trash2, Headphones, Zap } from 'lucide-react';\r\nimport { Meeting, TranscriptSegment, Speaker, MeetingMetadata } from '../types/meeting';\r\nimport { AudioPlayer } from './AudioPlayer';\r\n\r\ninterface ProfessionalTranscriptViewerProps {\r\n  meetings: Meeting[];\r\n  onDeleteMeeting: (meetingId: string) => void;\r\n  onGoToTranscription?: () => void;\r\n}\r\n\r\nconst SPEAKER_COLORS = [\r\n  'bg-blue-100 text-blue-800 border-blue-200',\r\n  'bg-green-100 text-green-800 border-green-200',\r\n  'bg-purple-100 text-purple-800 border-purple-200',\r\n  'bg-orange-100 text-orange-800 border-orange-200',\r\n  'bg-pink-100 text-pink-800 border-pink-200',\r\n  'bg-indigo-100 text-indigo-800 border-indigo-200',\r\n];\r\n\r\nexport const ProfessionalTranscriptViewer: React.FC<ProfessionalTranscriptViewerProps> = ({\r\n  meetings,\r\n  onDeleteMeeting,\r\n  onGoToTranscription,\r\n}) => {\r\n  const completedMeetings = meetings.filter(m => \r\n    m.transcriptionStatus.state === 'completed' && m.transcript\r\n  );\r\n\r\n  const formatTime = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const secs = Math.floor(seconds % 60);\r\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatDuration = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const hrs = Math.floor(mins / 60);\r\n    const remainingMins = mins % 60;\r\n    \r\n    if (hrs > 0) {\r\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n    }\r\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  return (\r\n    <div className=\"flex-1 flex flex-col animate-fade-in\">\r\n      {completedMeetings.length > 0 ? (\r\n        <div className=\"space-y-8 overflow-y-auto\">\r\n          {completedMeetings.map((meeting, meetingIndex) => (\r\n            <div \r\n              key={meeting.id} \r\n              className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 animate-fade-in-up\"\r\n              style={{ animationDelay: `${meetingIndex * 100}ms` }}\r\n            >\r\n              <div className=\"flex items-center justify-between mb-6\">\r\n                <div>\r\n                  <h3 className=\"text-xl sm:text-2xl font-bold text-white transition-colors duration-200 mb-2\">{meeting.title}</h3>\r\n                  <p className=\"text-base sm:text-lg text-white/70 transition-colors duration-200\">\r\n                    {meeting.date.toLocaleString('lt-LT')} • {formatDuration(meeting.duration)}s\r\n                  </p>\r\n                </div>\r\n                <button\r\n                  onClick={() => onDeleteMeeting(meeting.id)}\r\n                  className=\"p-3 text-white/60 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 transform hover:scale-110\"\r\n                  title=\"Ištrinti pokalbį\"\r\n                >\r\n                  <Trash2 className=\"h-5 w-5\" />\r\n                </button>\r\n              </div>\r\n              \r\n              {meeting.transcript && (\r\n                <div className=\"space-y-4\">\r\n                  {meeting.transcript.map((segment, index) => (\r\n                    <div \r\n                      key={index} \r\n                      className=\"bg-white/5 rounded-lg p-4 hover:bg-white/10 transition-all duration-300 transform hover:scale-[1.01] animate-fade-in-up\"\r\n                      style={{ animationDelay: `${(meetingIndex * 100) + (index * 20)}ms` }}\r\n                    >\r\n                      <div className=\"flex items-start space-x-4\">\r\n                        {meeting.participants && segment.speaker && (\r\n                          <div className=\"flex-shrink-0\">\r\n                            <div className=\"w-10 h-10 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-full flex items-center justify-center text-white text-sm font-medium transition-all duration-200 hover:scale-110\">\r\n                              {segment.speaker}\r\n                            </div>\r\n                          </div>\r\n                        )}\r\n                        <div className=\"flex-1\">\r\n                          <div className=\"flex items-center space-x-3 mb-2\">\r\n                            {meeting.participants && segment.speaker && (\r\n                              <span className=\"text-base font-semibold text-blue-400 transition-colors duration-200\">\r\n                                {segment.speaker}\r\n                              </span>\r\n                            )}\r\n                            <span className=\"text-sm text-white/50 transition-colors duration-200\">\r\n                              {formatTime(segment.timestamp)} - {formatTime(segment.endTimestamp || segment.timestamp)}\r\n                            </span>\r\n                          </div>\r\n                          <p className=\"text-base sm:text-lg text-white/90 leading-relaxed transition-colors duration-200\">{segment.text}</p>\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  ))}\r\n                </div>\r\n              )}\r\n            </div>\r\n          ))}\r\n        </div>\r\n      ) : (\r\n        <div className=\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-full flex items-center justify-center animate-pulse\">\r\n            <Headphones className=\"h-10 w-10 text-green-400\" />\r\n          </div>\r\n          <div>\r\n            <h3 className=\"text-xl sm:text-2xl font-semibold text-white\">Nėra transkribuotų pokalbių</h3>\r\n            <p className=\"text-base sm:text-lg text-white/70 mb-6\">\r\n              Transkribuokite pokalbį, kad pamatytumėte rezultatus čia\r\n            </p>\r\n            {onGoToTranscription && (\r\n              <button\r\n                onClick={onGoToTranscription}\r\n                className=\"inline-flex items-center space-x-2 px-6 py-3 text-base font-semibold text-white bg-gradient-to-r from-purple-500/80 via-purple-600/70 to-pink-600/80 hover:from-purple-500/90 hover:via-purple-600/80 hover:to-pink-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-purple-400/40 hover:border-purple-300/50 transform hover:scale-105\"\r\n              >\r\n                <Zap className=\"h-5 w-5\" />\r\n                <span>Eiti į transkribavimą</span>\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAA6B,OAAO,CAChD,OAAwGC,MAAM,CAAEC,UAAU,CAAEC,GAAG,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAUtJ,KAAM,CAAAC,cAAc,CAAG,CACrB,2CAA2C,CAC3C,8CAA8C,CAC9C,iDAAiD,CACjD,iDAAiD,CACjD,2CAA2C,CAC3C,iDAAiD,CAClD,CAED,MAAO,MAAM,CAAAC,4BAAyE,CAAGC,IAAA,EAInF,IAJoF,CACxFC,QAAQ,CACRC,eAAe,CACfC,mBACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,iBAAiB,CAAGH,QAAQ,CAACI,MAAM,CAACC,CAAC,EACzCA,CAAC,CAACC,mBAAmB,CAACC,KAAK,GAAK,WAAW,EAAIF,CAAC,CAACG,UACnD,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,OAAe,EAAa,CAC9C,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAI,IAAI,CAAGF,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,MAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACtD,CAAC,CAED,KAAM,CAAAC,cAAc,CAAIP,OAAe,EAAa,CAClD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAQ,GAAG,CAAGN,IAAI,CAACC,KAAK,CAACF,IAAI,CAAG,EAAE,CAAC,CACjC,KAAM,CAAAQ,aAAa,CAAGR,IAAI,CAAG,EAAE,CAE/B,GAAIO,GAAG,CAAG,CAAC,CAAE,CACX,MAAO,GAAGA,GAAG,IAAIC,aAAa,CAACJ,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI,CAACN,OAAO,CAAG,EAAE,EAAEK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC5G,CACA,MAAO,GAAGL,IAAI,IAAI,CAACD,OAAO,CAAG,EAAE,EAAEK,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAChE,CAAC,CAED,mBACEtB,IAAA,QAAK0B,SAAS,CAAC,sCAAsC,CAAAC,QAAA,CAClDlB,iBAAiB,CAACmB,MAAM,CAAG,CAAC,cAC3B5B,IAAA,QAAK0B,SAAS,CAAC,2BAA2B,CAAAC,QAAA,CACvClB,iBAAiB,CAACoB,GAAG,CAAC,CAACC,OAAO,CAAEC,YAAY,gBAC3C7B,KAAA,QAEEwB,SAAS,CAAC,sFAAsF,CAChGM,KAAK,CAAE,CAAEC,cAAc,CAAE,GAAGF,YAAY,CAAG,GAAG,IAAK,CAAE,CAAAJ,QAAA,eAErDzB,KAAA,QAAKwB,SAAS,CAAC,wCAAwC,CAAAC,QAAA,eACrDzB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAI0B,SAAS,CAAC,8EAA8E,CAAAC,QAAA,CAAEG,OAAO,CAACI,KAAK,CAAK,CAAC,cACjHhC,KAAA,MAAGwB,SAAS,CAAC,mEAAmE,CAAAC,QAAA,EAC7EG,OAAO,CAACK,IAAI,CAACC,cAAc,CAAC,OAAO,CAAC,CAAC,UAAG,CAACb,cAAc,CAACO,OAAO,CAACO,QAAQ,CAAC,CAAC,GAC7E,EAAG,CAAC,EACD,CAAC,cACNrC,IAAA,WACEsC,OAAO,CAAEA,CAAA,GAAM/B,eAAe,CAACuB,OAAO,CAACS,EAAE,CAAE,CAC3Cb,SAAS,CAAC,2HAA2H,CACrIQ,KAAK,CAAC,4BAAkB,CAAAP,QAAA,cAExB3B,IAAA,CAACJ,MAAM,EAAC8B,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CAAC,CAELI,OAAO,CAAChB,UAAU,eACjBd,IAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBG,OAAO,CAAChB,UAAU,CAACe,GAAG,CAAC,CAACW,OAAO,CAAEC,KAAK,gBACrCzC,IAAA,QAEE0B,SAAS,CAAC,yHAAyH,CACnIM,KAAK,CAAE,CAAEC,cAAc,CAAE,GAAIF,YAAY,CAAG,GAAG,CAAKU,KAAK,CAAG,EAAG,IAAK,CAAE,CAAAd,QAAA,cAEtEzB,KAAA,QAAKwB,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EACxCG,OAAO,CAACY,YAAY,EAAIF,OAAO,CAACG,OAAO,eACtC3C,IAAA,QAAK0B,SAAS,CAAC,eAAe,CAAAC,QAAA,cAC5B3B,IAAA,QAAK0B,SAAS,CAAC,wLAAwL,CAAAC,QAAA,CACpMa,OAAO,CAACG,OAAO,CACb,CAAC,CACH,CACN,cACDzC,KAAA,QAAKwB,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzB,KAAA,QAAKwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC9CG,OAAO,CAACY,YAAY,EAAIF,OAAO,CAACG,OAAO,eACtC3C,IAAA,SAAM0B,SAAS,CAAC,sEAAsE,CAAAC,QAAA,CACnFa,OAAO,CAACG,OAAO,CACZ,CACP,cACDzC,KAAA,SAAMwB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,EACnEZ,UAAU,CAACyB,OAAO,CAACI,SAAS,CAAC,CAAC,KAAG,CAAC7B,UAAU,CAACyB,OAAO,CAACK,YAAY,EAAIL,OAAO,CAACI,SAAS,CAAC,EACpF,CAAC,EACJ,CAAC,cACN5C,IAAA,MAAG0B,SAAS,CAAC,mFAAmF,CAAAC,QAAA,CAAEa,OAAO,CAACM,IAAI,CAAI,CAAC,EAChH,CAAC,EACH,CAAC,EAzBDL,KA0BF,CACN,CAAC,CACC,CACN,GArDIX,OAAO,CAACS,EAsDV,CACN,CAAC,CACC,CAAC,cAENrC,KAAA,QAAKwB,SAAS,CAAC,2FAA2F,CAAAC,QAAA,eACxG3B,IAAA,QAAK0B,SAAS,CAAC,6HAA6H,CAAAC,QAAA,cAC1I3B,IAAA,CAACH,UAAU,EAAC6B,SAAS,CAAC,0BAA0B,CAAE,CAAC,CAChD,CAAC,cACNxB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAI0B,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,4CAA2B,CAAI,CAAC,cAC7F3B,IAAA,MAAG0B,SAAS,CAAC,yCAAyC,CAAAC,QAAA,CAAC,yEAEvD,CAAG,CAAC,CACHnB,mBAAmB,eAClBN,KAAA,WACEoC,OAAO,CAAE9B,mBAAoB,CAC7BkB,SAAS,CAAC,+XAA+X,CAAAC,QAAA,eAEzY3B,IAAA,CAACF,GAAG,EAAC4B,SAAS,CAAC,SAAS,CAAE,CAAC,cAC3B1B,IAAA,SAAA2B,QAAA,CAAM,iCAAqB,CAAM,CAAC,EAC5B,CACT,EACE,CAAC,EACH,CACN,CACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}