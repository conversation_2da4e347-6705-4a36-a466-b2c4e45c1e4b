{"ast": null, "code": "import React,{useState}from'react';import{ChevronDown,ChevronRight,Calendar,Clock,Users,FileText,CheckCircle2,AlertCircle,Loader2,Download,Volume2}from'lucide-react';import{ProfessionalTranscriptViewer}from'./ProfessionalTranscriptViewer';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const CollapsibleTranscriptsList=_ref=>{let{meetings,onEditSegment,onExportMeeting}=_ref;const[expandedMeeting,setExpandedMeeting]=useState(null);const formatDuration=seconds=>{const mins=Math.floor(seconds/60);const hrs=Math.floor(mins/60);const remainingMins=mins%60;if(hrs>0){return`${hrs}:${remainingMins.toString().padStart(2,'0')}:${(seconds%60).toString().padStart(2,'0')}`;}return`${mins}:${(seconds%60).toString().padStart(2,'0')}`;};const formatDate=date=>{const now=new Date();const diffInHours=(now.getTime()-date.getTime())/(1000*60*60);if(diffInHours<24){return`Šiandien, ${date.toLocaleTimeString('lt-LT',{hour:'2-digit',minute:'2-digit'})}`;}else if(diffInHours<48){return`Vakar, ${date.toLocaleTimeString('lt-LT',{hour:'2-digit',minute:'2-digit'})}`;}else if(diffInHours<168){// 7 dienos\nreturn date.toLocaleDateString('lt-LT',{weekday:'long',hour:'2-digit',minute:'2-digit'});}else{return date.toLocaleDateString('lt-LT',{month:'long',day:'numeric',year:'numeric',hour:'2-digit',minute:'2-digit'});}};const getStatusIcon=meeting=>{const status=meeting.transcriptionStatus.state;switch(status){case'completed':return/*#__PURE__*/_jsx(CheckCircle2,{className:\"h-4 w-4 text-green-500\"});case'processing':return/*#__PURE__*/_jsx(Loader2,{className:\"h-4 w-4 text-blue-500 animate-spin\"});case'failed':return/*#__PURE__*/_jsx(AlertCircle,{className:\"h-4 w-4 text-red-500\"});case'pending':return/*#__PURE__*/_jsx(Clock,{className:\"h-4 w-4 text-yellow-500\"});default:return/*#__PURE__*/_jsx(FileText,{className:\"h-4 w-4 text-gray-400\"});}};const getStatusText=meeting=>{const status=meeting.transcriptionStatus.state;switch(status){case'completed':return'Baigtas';case'processing':return`Apdorojama${meeting.transcriptionStatus.progress?` (${meeting.transcriptionStatus.progress}%)`:''}`;case'failed':return'Nepavyko';case'pending':return'Eilėje';case'not_started':return meeting.audioBlob?'Galima transkribuoti':'Nėra audio';default:return'Nežinomas statusas';}};const getSpeakersSummary=meeting=>{if(!meeting.participants||meeting.participants.length===0){return'Kalbėtojai neidentifikuoti';}if(meeting.participants.length<=2){return meeting.participants.map(p=>p.name).join(', ');}return`${meeting.participants[0].name} ir ${meeting.participants.length-1} kiti`;};const getTranscriptStats=meeting=>{if(!meeting.transcript||meeting.transcript.length===0){return{segments:0,words:0,confidence:0};}const segments=meeting.transcript.length;const words=meeting.transcript.reduce((sum,seg)=>sum+(seg.wordsCount||seg.text.split(' ').length),0);const confidence=meeting.transcript.reduce((sum,seg)=>sum+(seg.confidence||0),0)/segments;return{segments,words,confidence:Math.round(confidence*100)};};const toggleExpanded=meetingId=>{setExpandedMeeting(expandedMeeting===meetingId?null:meetingId);};const hasTranscript=meeting=>{return meeting.transcriptionStatus.state==='completed'&&meeting.transcript&&meeting.transcript.length>0;};// Filter meetings that have been completed (audio recorded)\nconst completedMeetings=meetings.filter(meeting=>meeting.status==='completed'||meeting.status==='processing').sort((a,b)=>b.date.getTime()-a.date.getTime());if(completedMeetings.length===0){return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"gradient-border-fade rounded-3xl p-12 text-center shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mx-auto mb-6 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\",children:/*#__PURE__*/_jsx(FileText,{className:\"h-10 w-10 text-blue-500\"})}),/*#__PURE__*/_jsx(\"h2\",{className:\"text-xl font-semibold text-gray-900 mb-3\",children:\"N\\u0117ra pokalbi\\u0173\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600 max-w-md mx-auto mb-6\",children:\"Sukurkite ir u\\u017Ebaikite pokalbio \\u012Fra\\u0161ym\\u0105, kad pamatytum\\u0117te transkribavimo rezultatus \\u010Dia.\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col sm:flex-row gap-3 justify-center items-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83C\\uDFA4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\\u012Era\\u0161ykite pokalb\\u012F\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u26A1\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Transkribuokite su Whisper\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\uD83D\\uDCCA\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Per\\u017Ei\\u016Br\\u0117kite rezultatus\"})]})]})]})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[completedMeetings.length>0&&/*#__PURE__*/_jsxs(\"div\",{className:\"gradient-border-fade rounded-3xl p-6 shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary hover-gradient-shift float-effect\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-4\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:\"Pokalbi\\u0173 ap\\u017Evalga\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-sm text-gray-500\",children:[\"Atnaujinta: \",new Date().toLocaleTimeString('lt-LT',{hour:'2-digit',minute:'2-digit'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 lg:grid-cols-4 gap-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-blue-600 mb-1\",children:completedMeetings.length}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-700\",children:completedMeetings.length===1?'Pokalbis':'Pokalbiai'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-purple-600 mb-1\",children:completedMeetings.filter(m=>hasTranscript(m)).length}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-700\",children:completedMeetings.filter(m=>hasTranscript(m)).length===1?'Transkribuotas':'Transkribuoti'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-3xl font-bold text-green-600 mb-1\",children:[Math.round(completedMeetings.reduce((sum,m)=>sum+m.duration,0)/60),\"min\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-700\",children:\"Bendra trukm\\u0117\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-3xl font-bold text-orange-600 mb-1\",children:completedMeetings.filter(m=>hasTranscript(m)).reduce((sum,m)=>sum+getTranscriptStats(m).words,0).toLocaleString()}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm font-medium text-gray-700\",children:completedMeetings.filter(m=>hasTranscript(m)).reduce((sum,m)=>sum+getTranscriptStats(m).words,0)===1?'Žodis':'Žodžiai'})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-4 pt-4 border-t border-white/40\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-sm\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-gray-600\",children:\"Transkribavimo progresas:\"}),/*#__PURE__*/_jsx(\"span\",{className:\"font-medium text-gray-800\",children:completedMeetings.length>0?`${Math.round(completedMeetings.filter(m=>hasTranscript(m)).length/completedMeetings.length*100)}%`:'0%'})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-2 w-full bg-white/40 rounded-full h-2\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\",style:{width:completedMeetings.length>0?`${completedMeetings.filter(m=>hasTranscript(m)).length/completedMeetings.length*100}%`:'0%'}})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-lg font-semibold text-gray-900\",children:[\"Pokalbi\\u0173 istorija\",completedMeetings.length>0&&/*#__PURE__*/_jsxs(\"span\",{className:\"text-sm font-normal text-gray-500 ml-2\",children:[\"(\",completedMeetings.length,\")\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-gray-500\",children:\"R\\u016B\\u0161iuoti pagal dat\\u0105 (naujausi pirma)\"})]}),completedMeetings.map(meeting=>{const stats=getTranscriptStats(meeting);const isExpanded=expandedMeeting===meeting.id;return/*#__PURE__*/_jsxs(\"div\",{className:`gradient-border-fade rounded-3xl overflow-hidden transition-ultra hover-gradient-shift ${isExpanded?'shadow-primary bg-unique-gradient-2 scale-[1.02] pulse-subtle':'shadow-soft bg-unique-gradient-3 hover:shadow-elegant hover:scale-[1.01] float-effect'}`,children:[/*#__PURE__*/_jsx(\"div\",{className:`p-6 cursor-pointer transition-smooth ${isExpanded?'bg-gradient-to-r from-blue-50/40 via-purple-50/30 to-indigo-50/40 border-b border-gradient-fade':'hover:bg-gradient-to-r hover:from-white/50 hover:via-blue-50/30 hover:to-white/50'}`,onClick:()=>toggleExpanded(meeting.id),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"button\",{className:\"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors\",children:isExpanded?/*#__PURE__*/_jsx(ChevronDown,{className:\"h-5 w-5\"}):/*#__PURE__*/_jsx(ChevronRight,{className:\"h-5 w-5\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 mb-2\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-semibold text-gray-900 truncate\",children:meeting.title}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[getStatusIcon(meeting),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:getStatusText(meeting)})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-500\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(Calendar,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:formatDate(meeting.date)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(Clock,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:formatDuration(meeting.duration)})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(Users,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:getSpeakersSummary(meeting)})]}),hasTranscript(meeting)&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(FileText,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[stats.words.toLocaleString(),\" \\u017Eod\\u017Eiai (\",stats.confidence,\"% tikslumas)\"]})]})]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 flex-shrink-0\",children:[meeting.audioBlob&&/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.stopPropagation();// Play audio functionality could be added here\n},className:\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\",title:\"Klausyti audio\",children:/*#__PURE__*/_jsx(Volume2,{className:\"h-4 w-4\"})}),/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.stopPropagation();onExportMeeting(meeting);},className:\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\",title:\"Eksportuoti\",children:/*#__PURE__*/_jsx(Download,{className:\"h-4 w-4\"})})]})]})}),isExpanded&&/*#__PURE__*/_jsx(\"div\",{className:\"animate-fadeIn\",style:{animation:'fadeIn 0.3s ease-in-out'},children:hasTranscript(meeting)?/*#__PURE__*/_jsx(ProfessionalTranscriptViewer,{meetings:[meeting],onDeleteMeeting:()=>{}}):/*#__PURE__*/_jsxs(\"div\",{className:\"p-8 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\",children:meeting.transcriptionStatus.state==='processing'?/*#__PURE__*/_jsx(Loader2,{className:\"h-6 w-6 text-blue-500 animate-spin\"}):meeting.transcriptionStatus.state==='failed'?/*#__PURE__*/_jsx(AlertCircle,{className:\"h-6 w-6 text-red-500\"}):/*#__PURE__*/_jsx(FileText,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-medium text-gray-900 mb-2\",children:meeting.transcriptionStatus.state==='processing'?'Apdorojama transkribavimas':meeting.transcriptionStatus.state==='failed'?'Transkribavimas nepavyko':'Nėra transkribavimo'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:meeting.transcriptionStatus.state==='processing'?`Prašome palaukti... ${meeting.transcriptionStatus.progress||0}%`:meeting.transcriptionStatus.state==='failed'?meeting.transcriptionStatus.error||'Nežinoma klaida':meeting.audioBlob?'Eikite į transkribavimo skyrių, kad pradėtumėte':'Šis pokalbis neturi audio įrašo'}),meeting.transcriptionStatus.state==='processing'&&meeting.transcriptionStatus.progress&&/*#__PURE__*/_jsx(\"div\",{className:\"mt-4 w-64 bg-gray-200 rounded-full h-2 mx-auto\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-500 h-2 rounded-full transition-all duration-300\",style:{width:`${meeting.transcriptionStatus.progress}%`}})})]})})]},meeting.id);})]})]});};", "map": {"version": 3, "names": ["React", "useState", "ChevronDown", "ChevronRight", "Calendar", "Clock", "Users", "FileText", "CheckCircle2", "AlertCircle", "Loader2", "Download", "Volume2", "ProfessionalTranscriptViewer", "jsx", "_jsx", "jsxs", "_jsxs", "CollapsibleTranscriptsList", "_ref", "meetings", "onEditSegment", "onExportMeeting", "expandedMeeting", "setExpandedMeeting", "formatDuration", "seconds", "mins", "Math", "floor", "hrs", "remainingMins", "toString", "padStart", "formatDate", "date", "now", "Date", "diffInHours", "getTime", "toLocaleTimeString", "hour", "minute", "toLocaleDateString", "weekday", "month", "day", "year", "getStatusIcon", "meeting", "status", "transcriptionStatus", "state", "className", "getStatusText", "progress", "audioBlob", "getSpeakersSummary", "participants", "length", "map", "p", "name", "join", "getTranscriptStats", "transcript", "segments", "words", "confidence", "reduce", "sum", "seg", "wordsCount", "text", "split", "round", "toggleExpanded", "meetingId", "hasTranscript", "completedMeetings", "filter", "sort", "a", "b", "children", "m", "duration", "toLocaleString", "style", "width", "stats", "isExpanded", "id", "onClick", "title", "e", "stopPropagation", "animation", "onDeleteMeeting", "error"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/CollapsibleTranscriptsList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\r\nimport { ChevronDown, ChevronRight, Calendar, Clock, Users, FileText, CheckCircle2, AlertCircle, Loader2, Download, Edit3, Play, Volume2 } from 'lucide-react';\r\nimport { Meeting, Speaker } from '../types/meeting';\r\nimport { ProfessionalTranscriptViewer } from './ProfessionalTranscriptViewer';\r\n\r\ninterface CollapsibleTranscriptsListProps {\r\n  meetings: Meeting[];\r\n  onEditSegment?: (meetingId: string, segmentId: any, newText: any) => void;\r\n  onExportMeeting: (meeting: Meeting) => void;\r\n}\r\n\r\nexport const CollapsibleTranscriptsList: React.FC<CollapsibleTranscriptsListProps> = ({\r\n  meetings,\r\n  onEditSegment,\r\n  onExportMeeting,\r\n}) => {\r\n  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);\r\n\r\n  const formatDuration = (seconds: number): string => {\r\n    const mins = Math.floor(seconds / 60);\r\n    const hrs = Math.floor(mins / 60);\r\n    const remainingMins = mins % 60;\r\n    \r\n    if (hrs > 0) {\r\n      return `${hrs}:${remainingMins.toString().padStart(2, '0')}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n    }\r\n    return `${mins}:${(seconds % 60).toString().padStart(2, '0')}`;\r\n  };\r\n\r\n  const formatDate = (date: Date): string => {\r\n    const now = new Date();\r\n    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);\r\n    \r\n    if (diffInHours < 24) {\r\n      return `Šiandien, ${date.toLocaleTimeString('lt-LT', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      })}`;\r\n    } else if (diffInHours < 48) {\r\n      return `Vakar, ${date.toLocaleTimeString('lt-LT', { \r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      })}`;\r\n    } else if (diffInHours < 168) { // 7 dienos\r\n      return date.toLocaleDateString('lt-LT', { \r\n        weekday: 'long',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    } else {\r\n      return date.toLocaleDateString('lt-LT', { \r\n        month: 'long', \r\n        day: 'numeric',\r\n        year: 'numeric',\r\n        hour: '2-digit', \r\n        minute: '2-digit' \r\n      });\r\n    }\r\n  };\r\n\r\n  const getStatusIcon = (meeting: Meeting) => {\r\n    const status = meeting.transcriptionStatus.state;\r\n    \r\n    switch (status) {\r\n      case 'completed':\r\n        return <CheckCircle2 className=\"h-4 w-4 text-green-500\" />;\r\n      case 'processing':\r\n        return <Loader2 className=\"h-4 w-4 text-blue-500 animate-spin\" />;\r\n      case 'failed':\r\n        return <AlertCircle className=\"h-4 w-4 text-red-500\" />;\r\n      case 'pending':\r\n        return <Clock className=\"h-4 w-4 text-yellow-500\" />;\r\n      default:\r\n        return <FileText className=\"h-4 w-4 text-gray-400\" />;\r\n    }\r\n  };\r\n\r\n  const getStatusText = (meeting: Meeting) => {\r\n    const status = meeting.transcriptionStatus.state;\r\n    \r\n    switch (status) {\r\n      case 'completed':\r\n        return 'Baigtas';\r\n      case 'processing':\r\n        return `Apdorojama${meeting.transcriptionStatus.progress ? ` (${meeting.transcriptionStatus.progress}%)` : ''}`;\r\n      case 'failed':\r\n        return 'Nepavyko';\r\n      case 'pending':\r\n        return 'Eilėje';\r\n      case 'not_started':\r\n        return meeting.audioBlob ? 'Galima transkribuoti' : 'Nėra audio';\r\n      default:\r\n        return 'Nežinomas statusas';\r\n    }\r\n  };\r\n\r\n  const getSpeakersSummary = (meeting: Meeting) => {\r\n    if (!meeting.participants || meeting.participants.length === 0) {\r\n      return 'Kalbėtojai neidentifikuoti';\r\n    }\r\n    \r\n    if (meeting.participants.length <= 2) {\r\n      return meeting.participants.map(p => p.name).join(', ');\r\n    }\r\n    \r\n    return `${meeting.participants[0].name} ir ${meeting.participants.length - 1} kiti`;\r\n  };\r\n\r\n  const getTranscriptStats = (meeting: Meeting) => {\r\n    if (!meeting.transcript || meeting.transcript.length === 0) {\r\n      return { segments: 0, words: 0, confidence: 0 };\r\n    }\r\n    \r\n    const segments = meeting.transcript.length;\r\n    const words = meeting.transcript.reduce((sum, seg) => \r\n      sum + (seg.wordsCount || seg.text.split(' ').length), 0);\r\n    const confidence = meeting.transcript.reduce((sum, seg) => \r\n      sum + (seg.confidence || 0), 0) / segments;\r\n    \r\n    return { segments, words, confidence: Math.round(confidence * 100) };\r\n  };\r\n\r\n  const toggleExpanded = (meetingId: string) => {\r\n    setExpandedMeeting(expandedMeeting === meetingId ? null : meetingId);\r\n  };\r\n\r\n  const hasTranscript = (meeting: Meeting) => {\r\n    return meeting.transcriptionStatus.state === 'completed' && \r\n           meeting.transcript && \r\n           meeting.transcript.length > 0;\r\n  };\r\n\r\n  // Filter meetings that have been completed (audio recorded)\r\n  const completedMeetings = meetings.filter(meeting => \r\n    meeting.status === 'completed' || meeting.status === 'processing'\r\n  ).sort((a, b) => b.date.getTime() - a.date.getTime());\r\n\r\n  if (completedMeetings.length === 0) {\r\n    return (\r\n      <div className=\"space-y-6\">\r\n        {/* Empty State with Better Styling */}\r\n        <div className=\"gradient-border-fade rounded-3xl p-12 text-center shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary\">\r\n          <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mx-auto mb-6 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\">\r\n            <FileText className=\"h-10 w-10 text-blue-500\" />\r\n          </div>\r\n          <h2 className=\"text-xl font-semibold text-gray-900 mb-3\">Nėra pokalbių</h2>\r\n          <p className=\"text-gray-600 max-w-md mx-auto mb-6\">\r\n            Sukurkite ir užbaikite pokalbio įrašymą, kad pamatytumėte transkribavimo rezultatus čia.\r\n          </p>\r\n          \r\n          <div className=\"flex flex-col sm:flex-row gap-3 justify-center items-center\">\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>🎤</span>\r\n              <span>Įrašykite pokalbį</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>⚡</span>\r\n              <span>Transkribuokite su Whisper</span>\r\n            </div>\r\n            <div className=\"flex items-center space-x-2 text-sm text-gray-500 bg-white px-4 py-2 rounded-lg border\">\r\n              <span>📊</span>\r\n              <span>Peržiūrėkite rezultatus</span>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"space-y-6\">\r\n      {/* Summary Stats - Moved to Top */}\r\n      {completedMeetings.length > 0 && (\r\n                 <div className=\"gradient-border-fade rounded-3xl p-6 shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl transition-smooth hover:shadow-primary hover-gradient-shift float-effect\">\r\n          <div className=\"flex items-center justify-between mb-4\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900\">Pokalbių apžvalga</h2>\r\n            <div className=\"text-sm text-gray-500\">\r\n              Atnaujinta: {new Date().toLocaleTimeString('lt-LT', { \r\n                hour: '2-digit', \r\n                minute: '2-digit' \r\n              })}\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-blue-600 mb-1\">\r\n                {completedMeetings.length}\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings.length === 1 ? 'Pokalbis' : 'Pokalbiai'}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-purple-600 mb-1\">\r\n                {completedMeetings.filter(m => hasTranscript(m)).length}\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings.filter(m => hasTranscript(m)).length === 1 ? 'Transkribuotas' : 'Transkribuoti'}\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-green-600 mb-1\">\r\n                {Math.round(\r\n                  completedMeetings.reduce((sum, m) => sum + m.duration, 0) / 60\r\n                )}min\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">Bendra trukmė</div>\r\n            </div>\r\n            \r\n            <div className=\"bg-white/60 backdrop-blur-sm rounded-xl p-4 text-center border border-white/40\">\r\n              <div className=\"text-3xl font-bold text-orange-600 mb-1\">\r\n                {completedMeetings\r\n                  .filter(m => hasTranscript(m))\r\n                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0)\r\n                  .toLocaleString()\r\n                }\r\n              </div>\r\n              <div className=\"text-sm font-medium text-gray-700\">\r\n                {completedMeetings\r\n                  .filter(m => hasTranscript(m))\r\n                  .reduce((sum, m) => sum + getTranscriptStats(m).words, 0) === 1 ? 'Žodis' : 'Žodžiai'}\r\n              </div>\r\n            </div>\r\n          </div>\r\n          \r\n          {/* Progress Overview */}\r\n          <div className=\"mt-4 pt-4 border-t border-white/40\">\r\n            <div className=\"flex items-center justify-between text-sm\">\r\n              <span className=\"text-gray-600\">Transkribavimo progresas:</span>\r\n              <span className=\"font-medium text-gray-800\">\r\n                {completedMeetings.length > 0 \r\n                  ? `${Math.round((completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100)}%`\r\n                  : '0%'\r\n                }\r\n              </span>\r\n            </div>\r\n            <div className=\"mt-2 w-full bg-white/40 rounded-full h-2\">\r\n              <div \r\n                className=\"bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full transition-all duration-500\"\r\n                style={{ \r\n                  width: completedMeetings.length > 0 \r\n                    ? `${(completedMeetings.filter(m => hasTranscript(m)).length / completedMeetings.length) * 100}%`\r\n                    : '0%'\r\n                }}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      )}\r\n      \r\n      {/* Meetings List */}\r\n      <div className=\"space-y-3\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <h3 className=\"text-lg font-semibold text-gray-900\">\r\n            Pokalbių istorija\r\n            {completedMeetings.length > 0 && (\r\n              <span className=\"text-sm font-normal text-gray-500 ml-2\">\r\n                ({completedMeetings.length})\r\n              </span>\r\n            )}\r\n          </h3>\r\n          <div className=\"text-sm text-gray-500\">\r\n            Rūšiuoti pagal datą (naujausi pirma)\r\n          </div>\r\n        </div>\r\n      \r\n        {completedMeetings.map((meeting) => {\r\n          const stats = getTranscriptStats(meeting);\r\n          const isExpanded = expandedMeeting === meeting.id;\r\n          \r\n          return (\r\n                     <div\r\n             key={meeting.id}\r\n             className={`gradient-border-fade rounded-3xl overflow-hidden transition-ultra hover-gradient-shift ${\r\n               isExpanded \r\n                 ? 'shadow-primary bg-unique-gradient-2 scale-[1.02] pulse-subtle' \r\n                 : 'shadow-soft bg-unique-gradient-3 hover:shadow-elegant hover:scale-[1.01] float-effect'\r\n             }`}\r\n           >\r\n            {/* Meeting Header - Always Visible */}\r\n                         <div \r\n               className={`p-6 cursor-pointer transition-smooth ${\r\n                 isExpanded \r\n                   ? 'bg-gradient-to-r from-blue-50/40 via-purple-50/30 to-indigo-50/40 border-b border-gradient-fade' \r\n                   : 'hover:bg-gradient-to-r hover:from-white/50 hover:via-blue-50/30 hover:to-white/50'\r\n               }`}\r\n               onClick={() => toggleExpanded(meeting.id)}\r\n            >\r\n              <div className=\"flex items-center justify-between\">\r\n                <div className=\"flex items-center space-x-4 flex-1 min-w-0\">\r\n                  {/* Expand/Collapse Button */}\r\n                  <button className=\"flex-shrink-0 p-1 text-gray-400 hover:text-gray-600 transition-colors\">\r\n                    {isExpanded ? (\r\n                      <ChevronDown className=\"h-5 w-5\" />\r\n                    ) : (\r\n                      <ChevronRight className=\"h-5 w-5\" />\r\n                    )}\r\n                  </button>\r\n                  \r\n                  {/* Meeting Info */}\r\n                  <div className=\"flex-1 min-w-0\">\r\n                    <div className=\"flex items-center space-x-3 mb-2\">\r\n                      <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\r\n                        {meeting.title}\r\n                      </h3>\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        {getStatusIcon(meeting)}\r\n                        <span className=\"text-sm text-gray-600\">\r\n                          {getStatusText(meeting)}\r\n                        </span>\r\n                      </div>\r\n                    </div>\r\n                    \r\n                                         <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-3 text-sm text-gray-500\">\r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Calendar className=\"h-4 w-4\" />\r\n                        <span>{formatDate(meeting.date)}</span>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Clock className=\"h-4 w-4\" />\r\n                        <span>{formatDuration(meeting.duration)}</span>\r\n                      </div>\r\n                      \r\n                      <div className=\"flex items-center space-x-1\">\r\n                        <Users className=\"h-4 w-4\" />\r\n                        <span>{getSpeakersSummary(meeting)}</span>\r\n                      </div>\r\n                      \r\n                      {hasTranscript(meeting) && (\r\n                        <div className=\"flex items-center space-x-1\">\r\n                          <FileText className=\"h-4 w-4\" />\r\n                          <span>\r\n                            {stats.words.toLocaleString()} žodžiai \r\n                            ({stats.confidence}% tikslumas)\r\n                          </span>\r\n                        </div>\r\n                      )}\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n                \r\n                {/* Quick Actions */}\r\n                <div className=\"flex items-center space-x-2 flex-shrink-0\">\r\n                  {meeting.audioBlob && (\r\n                    <button \r\n                      onClick={(e) => {\r\n                        e.stopPropagation();\r\n                        // Play audio functionality could be added here\r\n                      }}\r\n                      className=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\"\r\n                      title=\"Klausyti audio\"\r\n                    >\r\n                      <Volume2 className=\"h-4 w-4\" />\r\n                    </button>\r\n                  )}\r\n                  \r\n                  <button \r\n                    onClick={(e) => {\r\n                      e.stopPropagation();\r\n                      onExportMeeting(meeting);\r\n                    }}\r\n                    className=\"p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100 transition-all duration-200\"\r\n                    title=\"Eksportuoti\"\r\n                  >\r\n                    <Download className=\"h-4 w-4\" />\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n            \r\n                         {/* Expanded Content - Transcript Viewer */}\r\n             {isExpanded && (\r\n               <div className=\"animate-fadeIn\"\r\n                    style={{\r\n                      animation: 'fadeIn 0.3s ease-in-out'\r\n                    }}\r\n               >\r\n                {hasTranscript(meeting) ? (\r\n                  <ProfessionalTranscriptViewer\r\n                    meetings={[meeting]}\r\n                    onDeleteMeeting={() => {}}\r\n                  />\r\n                ) : (\r\n                  <div className=\"p-8 text-center\">\r\n                    <div className=\"w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-3\">\r\n                      {meeting.transcriptionStatus.state === 'processing' ? (\r\n                        <Loader2 className=\"h-6 w-6 text-blue-500 animate-spin\" />\r\n                      ) : meeting.transcriptionStatus.state === 'failed' ? (\r\n                        <AlertCircle className=\"h-6 w-6 text-red-500\" />\r\n                      ) : (\r\n                        <FileText className=\"h-6 w-6 text-gray-400\" />\r\n                      )}\r\n                    </div>\r\n                    \r\n                    <h4 className=\"text-lg font-medium text-gray-900 mb-2\">\r\n                      {meeting.transcriptionStatus.state === 'processing' \r\n                        ? 'Apdorojama transkribavimas'\r\n                        : meeting.transcriptionStatus.state === 'failed'\r\n                        ? 'Transkribavimas nepavyko'\r\n                        : 'Nėra transkribavimo'\r\n                      }\r\n                    </h4>\r\n                    \r\n                    <p className=\"text-sm text-gray-500\">\r\n                      {meeting.transcriptionStatus.state === 'processing' \r\n                        ? `Prašome palaukti... ${meeting.transcriptionStatus.progress || 0}%`\r\n                        : meeting.transcriptionStatus.state === 'failed'\r\n                        ? meeting.transcriptionStatus.error || 'Nežinoma klaida'\r\n                        : meeting.audioBlob\r\n                        ? 'Eikite į transkribavimo skyrių, kad pradėtumėte'\r\n                        : 'Šis pokalbis neturi audio įrašo'\r\n                      }\r\n                    </p>\r\n                    \r\n                    {meeting.transcriptionStatus.state === 'processing' && \r\n                     meeting.transcriptionStatus.progress && (\r\n                      <div className=\"mt-4 w-64 bg-gray-200 rounded-full h-2 mx-auto\">\r\n                        <div \r\n                          className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\r\n                          style={{ width: `${meeting.transcriptionStatus.progress}%` }}\r\n                        />\r\n                      </div>\r\n                    )}\r\n                  </div>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n          );\r\n        })}\r\n      </div>\r\n    </div>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,CAAEC,YAAY,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAK,CAAEC,QAAQ,CAAEC,YAAY,CAAEC,WAAW,CAAEC,OAAO,CAAEC,QAAQ,CAAeC,OAAO,KAAQ,cAAc,CAE9J,OAASC,4BAA4B,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQ9E,MAAO,MAAM,CAAAC,0BAAqE,CAAGC,IAAA,EAI/E,IAJgF,CACpFC,QAAQ,CACRC,aAAa,CACbC,eACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAACI,eAAe,CAAEC,kBAAkB,CAAC,CAAGvB,QAAQ,CAAgB,IAAI,CAAC,CAE3E,KAAM,CAAAwB,cAAc,CAAIC,OAAe,EAAa,CAClD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAI,GAAG,CAAGF,IAAI,CAACC,KAAK,CAACF,IAAI,CAAG,EAAE,CAAC,CACjC,KAAM,CAAAI,aAAa,CAAGJ,IAAI,CAAG,EAAE,CAE/B,GAAIG,GAAG,CAAG,CAAC,CAAE,CACX,MAAO,GAAGA,GAAG,IAAIC,aAAa,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,IAAI,CAACP,OAAO,CAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAC5G,CACA,MAAO,GAAGN,IAAI,IAAI,CAACD,OAAO,CAAG,EAAE,EAAEM,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CAChE,CAAC,CAED,KAAM,CAAAC,UAAU,CAAIC,IAAU,EAAa,CACzC,KAAM,CAAAC,GAAG,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAC,WAAW,CAAG,CAACF,GAAG,CAACG,OAAO,CAAC,CAAC,CAAGJ,IAAI,CAACI,OAAO,CAAC,CAAC,GAAK,IAAI,CAAG,EAAE,CAAG,EAAE,CAAC,CAEvE,GAAID,WAAW,CAAG,EAAE,CAAE,CACpB,MAAO,aAAaH,IAAI,CAACK,kBAAkB,CAAC,OAAO,CAAE,CACnDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,EAAE,CACN,CAAC,IAAM,IAAIJ,WAAW,CAAG,EAAE,CAAE,CAC3B,MAAO,UAAUH,IAAI,CAACK,kBAAkB,CAAC,OAAO,CAAE,CAChDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,EAAE,CACN,CAAC,IAAM,IAAIJ,WAAW,CAAG,GAAG,CAAE,CAAE;AAC9B,MAAO,CAAAH,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAE,CACtCC,OAAO,CAAE,MAAM,CACfH,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CAAC,IAAM,CACL,MAAO,CAAAP,IAAI,CAACQ,kBAAkB,CAAC,OAAO,CAAE,CACtCE,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfN,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CACJ,CACF,CAAC,CAED,KAAM,CAAAM,aAAa,CAAIC,OAAgB,EAAK,CAC1C,KAAM,CAAAC,MAAM,CAAGD,OAAO,CAACE,mBAAmB,CAACC,KAAK,CAEhD,OAAQF,MAAM,EACZ,IAAK,WAAW,CACd,mBAAOnC,IAAA,CAACP,YAAY,EAAC6C,SAAS,CAAC,wBAAwB,CAAE,CAAC,CAC5D,IAAK,YAAY,CACf,mBAAOtC,IAAA,CAACL,OAAO,EAAC2C,SAAS,CAAC,oCAAoC,CAAE,CAAC,CACnE,IAAK,QAAQ,CACX,mBAAOtC,IAAA,CAACN,WAAW,EAAC4C,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACzD,IAAK,SAAS,CACZ,mBAAOtC,IAAA,CAACV,KAAK,EAACgD,SAAS,CAAC,yBAAyB,CAAE,CAAC,CACtD,QACE,mBAAOtC,IAAA,CAACR,QAAQ,EAAC8C,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIL,OAAgB,EAAK,CAC1C,KAAM,CAAAC,MAAM,CAAGD,OAAO,CAACE,mBAAmB,CAACC,KAAK,CAEhD,OAAQF,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,YAAY,CACf,MAAO,aAAaD,OAAO,CAACE,mBAAmB,CAACI,QAAQ,CAAG,KAAKN,OAAO,CAACE,mBAAmB,CAACI,QAAQ,IAAI,CAAG,EAAE,EAAE,CACjH,IAAK,QAAQ,CACX,MAAO,UAAU,CACnB,IAAK,SAAS,CACZ,MAAO,QAAQ,CACjB,IAAK,aAAa,CAChB,MAAO,CAAAN,OAAO,CAACO,SAAS,CAAG,sBAAsB,CAAG,YAAY,CAClE,QACE,MAAO,oBAAoB,CAC/B,CACF,CAAC,CAED,KAAM,CAAAC,kBAAkB,CAAIR,OAAgB,EAAK,CAC/C,GAAI,CAACA,OAAO,CAACS,YAAY,EAAIT,OAAO,CAACS,YAAY,CAACC,MAAM,GAAK,CAAC,CAAE,CAC9D,MAAO,4BAA4B,CACrC,CAEA,GAAIV,OAAO,CAACS,YAAY,CAACC,MAAM,EAAI,CAAC,CAAE,CACpC,MAAO,CAAAV,OAAO,CAACS,YAAY,CAACE,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACC,IAAI,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CACzD,CAEA,MAAO,GAAGd,OAAO,CAACS,YAAY,CAAC,CAAC,CAAC,CAACI,IAAI,OAAOb,OAAO,CAACS,YAAY,CAACC,MAAM,CAAG,CAAC,OAAO,CACrF,CAAC,CAED,KAAM,CAAAK,kBAAkB,CAAIf,OAAgB,EAAK,CAC/C,GAAI,CAACA,OAAO,CAACgB,UAAU,EAAIhB,OAAO,CAACgB,UAAU,CAACN,MAAM,GAAK,CAAC,CAAE,CAC1D,MAAO,CAAEO,QAAQ,CAAE,CAAC,CAAEC,KAAK,CAAE,CAAC,CAAEC,UAAU,CAAE,CAAE,CAAC,CACjD,CAEA,KAAM,CAAAF,QAAQ,CAAGjB,OAAO,CAACgB,UAAU,CAACN,MAAM,CAC1C,KAAM,CAAAQ,KAAK,CAAGlB,OAAO,CAACgB,UAAU,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GAC/CD,GAAG,EAAIC,GAAG,CAACC,UAAU,EAAID,GAAG,CAACE,IAAI,CAACC,KAAK,CAAC,GAAG,CAAC,CAACf,MAAM,CAAC,CAAE,CAAC,CAAC,CAC1D,KAAM,CAAAS,UAAU,CAAGnB,OAAO,CAACgB,UAAU,CAACI,MAAM,CAAC,CAACC,GAAG,CAAEC,GAAG,GACpDD,GAAG,EAAIC,GAAG,CAACH,UAAU,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAAGF,QAAQ,CAE5C,MAAO,CAAEA,QAAQ,CAAEC,KAAK,CAAEC,UAAU,CAAExC,IAAI,CAAC+C,KAAK,CAACP,UAAU,CAAG,GAAG,CAAE,CAAC,CACtE,CAAC,CAED,KAAM,CAAAQ,cAAc,CAAIC,SAAiB,EAAK,CAC5CrD,kBAAkB,CAACD,eAAe,GAAKsD,SAAS,CAAG,IAAI,CAAGA,SAAS,CAAC,CACtE,CAAC,CAED,KAAM,CAAAC,aAAa,CAAI7B,OAAgB,EAAK,CAC1C,MAAO,CAAAA,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,WAAW,EACjDH,OAAO,CAACgB,UAAU,EAClBhB,OAAO,CAACgB,UAAU,CAACN,MAAM,CAAG,CAAC,CACtC,CAAC,CAED;AACA,KAAM,CAAAoB,iBAAiB,CAAG3D,QAAQ,CAAC4D,MAAM,CAAC/B,OAAO,EAC/CA,OAAO,CAACC,MAAM,GAAK,WAAW,EAAID,OAAO,CAACC,MAAM,GAAK,YACvD,CAAC,CAAC+B,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAChD,IAAI,CAACI,OAAO,CAAC,CAAC,CAAG2C,CAAC,CAAC/C,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC,CAErD,GAAIwC,iBAAiB,CAACpB,MAAM,GAAK,CAAC,CAAE,CAClC,mBACE5C,IAAA,QAAKsC,SAAS,CAAC,WAAW,CAAA+B,QAAA,cAExBnE,KAAA,QAAKoC,SAAS,CAAC,gJAAgJ,CAAA+B,QAAA,eAC7JrE,IAAA,QAAKsC,SAAS,CAAC,wNAAwN,CAAA+B,QAAA,cACrOrE,IAAA,CAACR,QAAQ,EAAC8C,SAAS,CAAC,yBAAyB,CAAE,CAAC,CAC7C,CAAC,cACNtC,IAAA,OAAIsC,SAAS,CAAC,0CAA0C,CAAA+B,QAAA,CAAC,yBAAa,CAAI,CAAC,cAC3ErE,IAAA,MAAGsC,SAAS,CAAC,qCAAqC,CAAA+B,QAAA,CAAC,wHAEnD,CAAG,CAAC,cAEJnE,KAAA,QAAKoC,SAAS,CAAC,6DAA6D,CAAA+B,QAAA,eAC1EnE,KAAA,QAAKoC,SAAS,CAAC,wFAAwF,CAAA+B,QAAA,eACrGrE,IAAA,SAAAqE,QAAA,CAAM,cAAE,CAAM,CAAC,cACfrE,IAAA,SAAAqE,QAAA,CAAM,kCAAiB,CAAM,CAAC,EAC3B,CAAC,cACNnE,KAAA,QAAKoC,SAAS,CAAC,wFAAwF,CAAA+B,QAAA,eACrGrE,IAAA,SAAAqE,QAAA,CAAM,QAAC,CAAM,CAAC,cACdrE,IAAA,SAAAqE,QAAA,CAAM,4BAA0B,CAAM,CAAC,EACpC,CAAC,cACNnE,KAAA,QAAKoC,SAAS,CAAC,wFAAwF,CAAA+B,QAAA,eACrGrE,IAAA,SAAAqE,QAAA,CAAM,cAAE,CAAM,CAAC,cACfrE,IAAA,SAAAqE,QAAA,CAAM,wCAAuB,CAAM,CAAC,EACjC,CAAC,EACH,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAEA,mBACEnE,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAA+B,QAAA,EAEvBL,iBAAiB,CAACpB,MAAM,CAAG,CAAC,eAClB1C,KAAA,QAAKoC,SAAS,CAAC,qKAAqK,CAAA+B,QAAA,eAC3LnE,KAAA,QAAKoC,SAAS,CAAC,wCAAwC,CAAA+B,QAAA,eACrDrE,IAAA,OAAIsC,SAAS,CAAC,qCAAqC,CAAA+B,QAAA,CAAC,6BAAiB,CAAI,CAAC,cAC1EnE,KAAA,QAAKoC,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,EAAC,cACzB,CAAC,GAAI,CAAA/C,IAAI,CAAC,CAAC,CAACG,kBAAkB,CAAC,OAAO,CAAE,CAClDC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,EACC,CAAC,EACH,CAAC,cAENzB,KAAA,QAAKoC,SAAS,CAAC,uCAAuC,CAAA+B,QAAA,eACpDnE,KAAA,QAAKoC,SAAS,CAAC,gFAAgF,CAAA+B,QAAA,eAC7FrE,IAAA,QAAKsC,SAAS,CAAC,uCAAuC,CAAA+B,QAAA,CACnDL,iBAAiB,CAACpB,MAAM,CACtB,CAAC,cACN5C,IAAA,QAAKsC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,CAC/CL,iBAAiB,CAACpB,MAAM,GAAK,CAAC,CAAG,UAAU,CAAG,WAAW,CACvD,CAAC,EACH,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,gFAAgF,CAAA+B,QAAA,eAC7FrE,IAAA,QAAKsC,SAAS,CAAC,yCAAyC,CAAA+B,QAAA,CACrDL,iBAAiB,CAACC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,CACpD,CAAC,cACN5C,IAAA,QAAKsC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,CAC/CL,iBAAiB,CAACC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,GAAK,CAAC,CAAG,gBAAgB,CAAG,eAAe,CAC/F,CAAC,EACH,CAAC,cAEN1C,KAAA,QAAKoC,SAAS,CAAC,gFAAgF,CAAA+B,QAAA,eAC7FnE,KAAA,QAAKoC,SAAS,CAAC,wCAAwC,CAAA+B,QAAA,EACpDxD,IAAI,CAAC+C,KAAK,CACTI,iBAAiB,CAACV,MAAM,CAAC,CAACC,GAAG,CAAEe,CAAC,GAAKf,GAAG,CAAGe,CAAC,CAACC,QAAQ,CAAE,CAAC,CAAC,CAAG,EAC9D,CAAC,CAAC,KACJ,EAAK,CAAC,cACNvE,IAAA,QAAKsC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,CAAC,oBAAa,CAAK,CAAC,EACnE,CAAC,cAENnE,KAAA,QAAKoC,SAAS,CAAC,gFAAgF,CAAA+B,QAAA,eAC7FrE,IAAA,QAAKsC,SAAS,CAAC,yCAAyC,CAAA+B,QAAA,CACrDL,iBAAiB,CACfC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAC7BhB,MAAM,CAAC,CAACC,GAAG,CAAEe,CAAC,GAAKf,GAAG,CAAGN,kBAAkB,CAACqB,CAAC,CAAC,CAAClB,KAAK,CAAE,CAAC,CAAC,CACxDoB,cAAc,CAAC,CAAC,CAEhB,CAAC,cACNxE,IAAA,QAAKsC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,CAC/CL,iBAAiB,CACfC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAC7BhB,MAAM,CAAC,CAACC,GAAG,CAAEe,CAAC,GAAKf,GAAG,CAAGN,kBAAkB,CAACqB,CAAC,CAAC,CAAClB,KAAK,CAAE,CAAC,CAAC,GAAK,CAAC,CAAG,OAAO,CAAG,SAAS,CACpF,CAAC,EACH,CAAC,EACH,CAAC,cAGNlD,KAAA,QAAKoC,SAAS,CAAC,oCAAoC,CAAA+B,QAAA,eACjDnE,KAAA,QAAKoC,SAAS,CAAC,2CAA2C,CAAA+B,QAAA,eACxDrE,IAAA,SAAMsC,SAAS,CAAC,eAAe,CAAA+B,QAAA,CAAC,2BAAyB,CAAM,CAAC,cAChErE,IAAA,SAAMsC,SAAS,CAAC,2BAA2B,CAAA+B,QAAA,CACxCL,iBAAiB,CAACpB,MAAM,CAAG,CAAC,CACzB,GAAG/B,IAAI,CAAC+C,KAAK,CAAEI,iBAAiB,CAACC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,CAAGoB,iBAAiB,CAACpB,MAAM,CAAI,GAAG,CAAC,GAAG,CAC3G,IAAI,CAEJ,CAAC,EACJ,CAAC,cACN5C,IAAA,QAAKsC,SAAS,CAAC,0CAA0C,CAAA+B,QAAA,cACvDrE,IAAA,QACEsC,SAAS,CAAC,2FAA2F,CACrGmC,KAAK,CAAE,CACLC,KAAK,CAAEV,iBAAiB,CAACpB,MAAM,CAAG,CAAC,CAC/B,GAAIoB,iBAAiB,CAACC,MAAM,CAACK,CAAC,EAAIP,aAAa,CAACO,CAAC,CAAC,CAAC,CAAC1B,MAAM,CAAGoB,iBAAiB,CAACpB,MAAM,CAAI,GAAG,GAAG,CAC/F,IACN,CAAE,CACH,CAAC,CACC,CAAC,EACH,CAAC,EACH,CACN,cAGD1C,KAAA,QAAKoC,SAAS,CAAC,WAAW,CAAA+B,QAAA,eACxBnE,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,eAChDnE,KAAA,OAAIoC,SAAS,CAAC,qCAAqC,CAAA+B,QAAA,EAAC,wBAElD,CAACL,iBAAiB,CAACpB,MAAM,CAAG,CAAC,eAC3B1C,KAAA,SAAMoC,SAAS,CAAC,wCAAwC,CAAA+B,QAAA,EAAC,GACtD,CAACL,iBAAiB,CAACpB,MAAM,CAAC,GAC7B,EAAM,CACP,EACC,CAAC,cACL5C,IAAA,QAAKsC,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,CAAC,qDAEvC,CAAK,CAAC,EACH,CAAC,CAELL,iBAAiB,CAACnB,GAAG,CAAEX,OAAO,EAAK,CAClC,KAAM,CAAAyC,KAAK,CAAG1B,kBAAkB,CAACf,OAAO,CAAC,CACzC,KAAM,CAAA0C,UAAU,CAAGpE,eAAe,GAAK0B,OAAO,CAAC2C,EAAE,CAEjD,mBACW3E,KAAA,QAERoC,SAAS,CAAE,0FACTsC,UAAU,CACN,+DAA+D,CAC/D,uFAAuF,EAC1F,CAAAP,QAAA,eAGSrE,IAAA,QACVsC,SAAS,CAAE,wCACTsC,UAAU,CACN,iGAAiG,CACjG,mFAAmF,EACtF,CACHE,OAAO,CAAEA,CAAA,GAAMjB,cAAc,CAAC3B,OAAO,CAAC2C,EAAE,CAAE,CAAAR,QAAA,cAE3CnE,KAAA,QAAKoC,SAAS,CAAC,mCAAmC,CAAA+B,QAAA,eAChDnE,KAAA,QAAKoC,SAAS,CAAC,4CAA4C,CAAA+B,QAAA,eAEzDrE,IAAA,WAAQsC,SAAS,CAAC,uEAAuE,CAAA+B,QAAA,CACtFO,UAAU,cACT5E,IAAA,CAACb,WAAW,EAACmD,SAAS,CAAC,SAAS,CAAE,CAAC,cAEnCtC,IAAA,CAACZ,YAAY,EAACkD,SAAS,CAAC,SAAS,CAAE,CACpC,CACK,CAAC,cAGTpC,KAAA,QAAKoC,SAAS,CAAC,gBAAgB,CAAA+B,QAAA,eAC7BnE,KAAA,QAAKoC,SAAS,CAAC,kCAAkC,CAAA+B,QAAA,eAC/CrE,IAAA,OAAIsC,SAAS,CAAC,8CAA8C,CAAA+B,QAAA,CACzDnC,OAAO,CAAC6C,KAAK,CACZ,CAAC,cACL7E,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,EACzCpC,aAAa,CAACC,OAAO,CAAC,cACvBlC,IAAA,SAAMsC,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,CACpC9B,aAAa,CAACL,OAAO,CAAC,CACnB,CAAC,EACJ,CAAC,EACH,CAAC,cAEehC,KAAA,QAAKoC,SAAS,CAAC,6DAA6D,CAAA+B,QAAA,eAC/FnE,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAC1CrE,IAAA,CAACX,QAAQ,EAACiD,SAAS,CAAC,SAAS,CAAE,CAAC,cAChCtC,IAAA,SAAAqE,QAAA,CAAOlD,UAAU,CAACe,OAAO,CAACd,IAAI,CAAC,CAAO,CAAC,EACpC,CAAC,cAENlB,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAC1CrE,IAAA,CAACV,KAAK,EAACgD,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7BtC,IAAA,SAAAqE,QAAA,CAAO3D,cAAc,CAACwB,OAAO,CAACqC,QAAQ,CAAC,CAAO,CAAC,EAC5C,CAAC,cAENrE,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAC1CrE,IAAA,CAACT,KAAK,EAAC+C,SAAS,CAAC,SAAS,CAAE,CAAC,cAC7BtC,IAAA,SAAAqE,QAAA,CAAO3B,kBAAkB,CAACR,OAAO,CAAC,CAAO,CAAC,EACvC,CAAC,CAEL6B,aAAa,CAAC7B,OAAO,CAAC,eACrBhC,KAAA,QAAKoC,SAAS,CAAC,6BAA6B,CAAA+B,QAAA,eAC1CrE,IAAA,CAACR,QAAQ,EAAC8C,SAAS,CAAC,SAAS,CAAE,CAAC,cAChCpC,KAAA,SAAAmE,QAAA,EACGM,KAAK,CAACvB,KAAK,CAACoB,cAAc,CAAC,CAAC,CAAC,sBAC7B,CAACG,KAAK,CAACtB,UAAU,CAAC,cACrB,EAAM,CAAC,EACJ,CACN,EACE,CAAC,EACH,CAAC,EACH,CAAC,cAGNnD,KAAA,QAAKoC,SAAS,CAAC,2CAA2C,CAAA+B,QAAA,EACvDnC,OAAO,CAACO,SAAS,eAChBzC,IAAA,WACE8E,OAAO,CAAGE,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB;AACF,CAAE,CACF3C,SAAS,CAAC,gGAAgG,CAC1GyC,KAAK,CAAC,gBAAgB,CAAAV,QAAA,cAEtBrE,IAAA,CAACH,OAAO,EAACyC,SAAS,CAAC,SAAS,CAAE,CAAC,CACzB,CACT,cAEDtC,IAAA,WACE8E,OAAO,CAAGE,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnB1E,eAAe,CAAC2B,OAAO,CAAC,CAC1B,CAAE,CACFI,SAAS,CAAC,gGAAgG,CAC1GyC,KAAK,CAAC,aAAa,CAAAV,QAAA,cAEnBrE,IAAA,CAACJ,QAAQ,EAAC0C,SAAS,CAAC,SAAS,CAAE,CAAC,CAC1B,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CAGJsC,UAAU,eACT5E,IAAA,QAAKsC,SAAS,CAAC,gBAAgB,CAC1BmC,KAAK,CAAE,CACLS,SAAS,CAAE,yBACb,CAAE,CAAAb,QAAA,CAELN,aAAa,CAAC7B,OAAO,CAAC,cACrBlC,IAAA,CAACF,4BAA4B,EAC3BO,QAAQ,CAAE,CAAC6B,OAAO,CAAE,CACpBiD,eAAe,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC3B,CAAC,cAEFjF,KAAA,QAAKoC,SAAS,CAAC,iBAAiB,CAAA+B,QAAA,eAC9BrE,IAAA,QAAKsC,SAAS,CAAC,kFAAkF,CAAA+B,QAAA,CAC9FnC,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,YAAY,cACjDrC,IAAA,CAACL,OAAO,EAAC2C,SAAS,CAAC,oCAAoC,CAAE,CAAC,CACxDJ,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,QAAQ,cAChDrC,IAAA,CAACN,WAAW,EAAC4C,SAAS,CAAC,sBAAsB,CAAE,CAAC,cAEhDtC,IAAA,CAACR,QAAQ,EAAC8C,SAAS,CAAC,uBAAuB,CAAE,CAC9C,CACE,CAAC,cAENtC,IAAA,OAAIsC,SAAS,CAAC,wCAAwC,CAAA+B,QAAA,CACnDnC,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,YAAY,CAC/C,4BAA4B,CAC5BH,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,QAAQ,CAC9C,0BAA0B,CAC1B,qBAAqB,CAEvB,CAAC,cAELrC,IAAA,MAAGsC,SAAS,CAAC,uBAAuB,CAAA+B,QAAA,CACjCnC,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,YAAY,CAC/C,uBAAuBH,OAAO,CAACE,mBAAmB,CAACI,QAAQ,EAAI,CAAC,GAAG,CACnEN,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,QAAQ,CAC9CH,OAAO,CAACE,mBAAmB,CAACgD,KAAK,EAAI,iBAAiB,CACtDlD,OAAO,CAACO,SAAS,CACjB,iDAAiD,CACjD,iCAAiC,CAEpC,CAAC,CAEHP,OAAO,CAACE,mBAAmB,CAACC,KAAK,GAAK,YAAY,EAClDH,OAAO,CAACE,mBAAmB,CAACI,QAAQ,eACnCxC,IAAA,QAAKsC,SAAS,CAAC,gDAAgD,CAAA+B,QAAA,cAC7DrE,IAAA,QACEsC,SAAS,CAAC,0DAA0D,CACpEmC,KAAK,CAAE,CAAEC,KAAK,CAAE,GAAGxC,OAAO,CAACE,mBAAmB,CAACI,QAAQ,GAAI,CAAE,CAC9D,CAAC,CACC,CACN,EACE,CACN,CACE,CACN,GA3JKN,OAAO,CAAC2C,EA4JX,CAAC,CAER,CAAC,CAAC,EACC,CAAC,EACH,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}