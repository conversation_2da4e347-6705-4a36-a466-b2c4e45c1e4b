{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useCallback } from 'react';\nimport { MeetingsList, ErrorBoundary, WhisperStatusIndicator, TranscriptionManager, ProfessionalTranscriptViewer, RecordingPanel, GridControls } from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { createDemoMeetings } from './utils/demoData';\nimport { Headphones, Plus, Mic2, TestTube, Zap, List } from 'lucide-react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  _s();\n  const [meetings, setMeetings] = useState([]);\n  const [currentMeeting, setCurrentMeeting] = useState(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState(null);\n  const [activeView, setActiveView] = useState('recording');\n\n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n  const {\n    recordingState,\n    startRecording,\n    stopRecording,\n    pauseRecording,\n    resumeRecording\n  } = useAudioRecorder();\n  const {\n    transcript,\n    isTranscribing,\n    transcribeAudioEnhanced,\n    cancelTranscription,\n    editSegment,\n    clearTranscript,\n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n  const handleStartRecording = useCallback(async title => {\n    try {\n      await startRecording();\n      const newMeeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started'\n        }\n      };\n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      if (currentMeeting && audioBlob) {\n        const updatedMeeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started'\n          }\n        };\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m));\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n  const handleStartTranscription = useCallback(async meetingId => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date()\n      }\n    };\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: progress => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: {\n              ...m.transcriptionStatus,\n              progress,\n              state: 'processing'\n            }\n          } : m));\n        },\n        onStatusUpdate: status => {\n          setMeetings(prev => prev.map(m => m.id === meetingId ? {\n            ...m,\n            transcriptionStatus: status\n          } : m));\n        },\n        enhanceSpeakers: true\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date()\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n\n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence\n      });\n    } catch (error) {\n      console.error('❌ Transkribavimo klaida:', error);\n      const errorMeeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt\n        }\n      };\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n  const handleCancelTranscription = useCallback(meetingId => {\n    cancelTranscription();\n    setMeetings(prev => prev.map(m => m.id === meetingId ? {\n      ...m,\n      transcriptionStatus: {\n        ...m.transcriptionStatus,\n        state: 'cancelled'\n      }\n    } : m));\n  }, [cancelTranscription]);\n  const handleEditSegment = useCallback((meetingId, segmentId, newText) => {\n    editSegment(segmentId, newText);\n\n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => {\n      var _meeting$transcript;\n      return meeting.id === meetingId ? {\n        ...meeting,\n        transcript: (_meeting$transcript = meeting.transcript) === null || _meeting$transcript === void 0 ? void 0 : _meeting$transcript.map(segment => segment.id === segmentId ? {\n          ...segment,\n          text: newText,\n          isEdited: true,\n          editedAt: new Date(),\n          editedBy: 'user'\n        } : segment)\n      } : meeting;\n    }));\n  }, [editSegment]);\n  const handleSelectMeeting = useCallback(meeting => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n  const handleDeleteMeeting = useCallback(meetingId => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if ((currentMeeting === null || currentMeeting === void 0 ? void 0 : currentMeeting.id) === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if ((selectedMeetingForTranscript === null || selectedMeetingForTranscript === void 0 ? void 0 : selectedMeetingForTranscript.id) === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n  const handleExportMeeting = useCallback(meeting => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus\n    };\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,' + encodeURIComponent(dataStr);\n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed',\n        progress: 100,\n        completedAt: meeting.date\n      }\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `min-h-screen bg-app-gradient font-inter relative overflow-hidden ${activeView === 'recording' ? 'grid-page-recording' : activeView === 'transcription' ? 'grid-page-transcription' : activeView === 'transcript' ? 'grid-page-results' : 'grid-page-home'}`,\n      style: {\n        '--grid-size': `${gridSize}px`,\n        '--grid-rotation': `${gridRotation}deg`,\n        '--grid-color': gridColor\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gradient-to-tr from-purple-900/06 via-transparent via-purple-800/04 to-slate-900/08 pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-gradient-to-bl from-transparent via-purple-700/03 via-slate-800/02 to-purple-900/04 pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 302,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed top-20 left-20 w-96 h-96 bg-gradient-radial from-purple-600/08 to-transparent rounded-full pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 304,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed bottom-20 right-20 w-80 h-80 bg-gradient-radial from-purple-500/06 to-transparent rounded-full pointer-events-none\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"relative z-10\",\n        children: [/*#__PURE__*/_jsxDEV(\"header\", {\n          className: \"fixed top-0 left-0 right-0 z-50\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"glassmorphism border-b border-white/10\",\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"px-4 sm:px-6 py-2.5\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2.5\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-7 h-7 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-lg flex items-center justify-center shadow-md\",\n                    children: /*#__PURE__*/_jsxDEV(Mic2, {\n                      className: \"h-3.5 w-3.5 text-white drop-shadow-sm\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 316,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 315,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden sm:block\",\n                    children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n                      className: \"text-sm font-bold text-white drop-shadow-sm\",\n                      children: \"MOM App\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 319,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-white/60 leading-none\",\n                      children: \"Meeting Recording\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 320,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 318,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"sm:hidden\",\n                    children: /*#__PURE__*/_jsxDEV(\"h1\", {\n                      className: \"text-sm font-bold text-white drop-shadow-sm\",\n                      children: \"MOM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 323,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 322,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-2 sm:space-x-4\",\n                  children: [/*#__PURE__*/_jsxDEV(WhisperStatusIndicator, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 330,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"hidden sm:block w-px h-6 bg-white/20\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 333,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-1 sm:space-x-2\",\n                    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setActiveView('recording'),\n                      className: `inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${activeView === 'recording' ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105' : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'}`,\n                      children: [/*#__PURE__*/_jsxDEV(Mic2, {\n                        className: \"h-4 w-4 transition-transform duration-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 345,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hidden xs:inline\",\n                        children: \"\\u012Era\\u0161ymas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 346,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 337,\n                      columnNumber: 13\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setActiveView('transcription'),\n                      className: `inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${activeView === 'transcription' ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105' : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'}`,\n                      children: [/*#__PURE__*/_jsxDEV(Zap, {\n                        className: \"h-4 w-4 transition-transform duration-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 356,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hidden xs:inline\",\n                        children: \"Transkribavimas\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 357,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 348,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => setActiveView('transcript'),\n                      className: `inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${activeView === 'transcript' ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105' : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'}`,\n                      children: [/*#__PURE__*/_jsxDEV(Headphones, {\n                        className: \"h-4 w-4 transition-transform duration-200\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 367,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hidden xs:inline\",\n                        children: \"Rezultatai\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 368,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 359,\n                      columnNumber: 19\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 336,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-px h-6 bg-white/20\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 373,\n                    columnNumber: 17\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center space-x-2 sm:space-x-3\",\n                    children: [meetings.length === 0 && /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: loadDemoData,\n                      className: \"inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium text-white/80 hover:text-white bg-gradient-to-r from-amber-500/20 via-orange-500/15 to-amber-500/20 hover:from-amber-500/30 hover:via-orange-500/25 hover:to-amber-500/30 backdrop-blur-md border border-amber-400/30 hover:border-amber-300/40 rounded-lg shadow-md hover:shadow-lg transition-all duration-300\",\n                      title: \"U\\u017Ekrauti demonstracinius duomenis\",\n                      children: [/*#__PURE__*/_jsxDEV(TestTube, {\n                        className: \"h-4 w-4 text-amber-300\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 383,\n                        columnNumber: 21\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hidden xs:inline\",\n                        children: \"Demo\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 384,\n                        columnNumber: 21\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 378,\n                      columnNumber: 19\n                    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                      onClick: () => {\n                        setActiveView('recording');\n                        // Automatiškai pradėti naują įrašymą\n                        if (!recordingState.isRecording) {\n                          handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                        }\n                      },\n                      className: \"inline-flex items-center space-x-1.5 sm:space-x-2 px-4 sm:px-5 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\",\n                      children: [/*#__PURE__*/_jsxDEV(Plus, {\n                        className: \"h-4 w-4 drop-shadow-sm transition-transform duration-200 group-hover:rotate-90\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 398,\n                        columnNumber: 19\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"hidden xs:inline drop-shadow-sm\",\n                        children: \"Naujas pokalbis\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 399,\n                        columnNumber: 19\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 388,\n                      columnNumber: 17\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 376,\n                    columnNumber: 17\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 328,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 312,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 311,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 310,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 309,\n          columnNumber: 9\n        }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n          className: \"pt-20 sm:pt-24 md:pt-32 px-3 sm:px-6 md:px-8 pb-4 sm:pb-6 md:pb-8\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 h-[calc(100vh-120px)] sm:h-[calc(100vh-140px)]\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-2 space-y-4 sm:space-y-6\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"relative h-full\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 transition-all duration-500 ease-in-out ${activeView === 'recording' ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-4 pointer-events-none'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center justify-between mb-4 sm:mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"flex items-center space-x-3 sm:space-x-4\",\n                        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-xl flex items-center justify-center shadow-lg\",\n                          children: /*#__PURE__*/_jsxDEV(Mic2, {\n                            className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 429,\n                            columnNumber: 27\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 428,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                            className: \"text-xl sm:text-2xl font-bold text-white\",\n                            children: \"Pokalbio \\u012Fra\\u0161ymas\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 432,\n                            columnNumber: 27\n                          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-sm sm:text-base text-white/70\",\n                            children: \"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 433,\n                            columnNumber: 27\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 431,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 427,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n                        onClick: () => {\n                          if (!recordingState.isRecording) {\n                            handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                          }\n                        },\n                        className: \"inline-flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 hidden sm:inline-flex\",\n                        children: [/*#__PURE__*/_jsxDEV(Plus, {\n                          className: \"h-4 w-4\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 444,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                          children: \"Naujas pokalbis\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 445,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 436,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 426,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 flex flex-col\",\n                      children: /*#__PURE__*/_jsxDEV(RecordingPanel, {\n                        recordingState: recordingState,\n                        currentMeeting: currentMeeting,\n                        onStartRecording: handleStartRecording,\n                        onStopRecording: handleStopRecording,\n                        onPauseRecording: () => {},\n                        onResumeRecording: () => {}\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 450,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 449,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 425,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 420,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 transition-all duration-500 ease-in-out ${activeView === 'transcription' ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4 pointer-events-none'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 rounded-xl flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/_jsxDEV(Zap, {\n                          className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 471,\n                          columnNumber: 25\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 470,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-xl sm:text-2xl font-bold text-white\",\n                          children: \"Transkribavimas\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 474,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm sm:text-base text-white/70\",\n                          children: \"Audio fail\\u0173 transkribavimas \\u012F tekst\\u0105\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 475,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 473,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 469,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 flex flex-col\",\n                      children: /*#__PURE__*/_jsxDEV(TranscriptionManager, {\n                        meetings: meetings,\n                        onStartTranscription: handleStartTranscription,\n                        onCancelTranscription: handleCancelTranscription,\n                        isTranscribing: isTranscribing,\n                        currentTranscriptionId: currentTranscriptionId,\n                        onDeleteMeeting: handleDeleteMeeting,\n                        onViewResults: () => setActiveView('transcript')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 480,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 479,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 468,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 463,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: `absolute inset-0 transition-all duration-500 ease-in-out ${activeView === 'transcript' ? 'opacity-100 translate-x-0' : 'opacity-0 translate-x-4 pointer-events-none'}`,\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\",\n                    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\",\n                      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 rounded-xl flex items-center justify-center shadow-lg\",\n                        children: /*#__PURE__*/_jsxDEV(Headphones, {\n                          className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 502,\n                          columnNumber: 25\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                          className: \"text-xl sm:text-2xl font-bold text-white\",\n                          children: \"Rezultatai\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 505,\n                          columnNumber: 25\n                        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm sm:text-base text-white/70\",\n                          children: \"Per\\u017Ei\\u016Br\\u0117kite transkribavimo rezultatus\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 506,\n                          columnNumber: 25\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 504,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 500,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 flex flex-col\",\n                      children: /*#__PURE__*/_jsxDEV(ProfessionalTranscriptViewer, {\n                        meetings: meetings,\n                        onDeleteMeeting: handleDeleteMeeting,\n                        onGoToTranscription: () => setActiveView('transcription')\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 511,\n                        columnNumber: 23\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 510,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 499,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 494,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 418,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 417,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"lg:col-span-1\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 rounded-xl flex items-center justify-center shadow-lg\",\n                    children: /*#__PURE__*/_jsxDEV(List, {\n                      className: \"h-5 w-5 sm:h-6 sm:w-6 text-white\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 527,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 526,\n                    columnNumber: 19\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n                      className: \"text-xl sm:text-2xl font-bold text-white\",\n                      children: \"Pokalbiai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 530,\n                      columnNumber: 21\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm sm:text-base text-white/70\",\n                      children: \"Visi j\\u016Bs\\u0173 pokalbiai\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 531,\n                      columnNumber: 21\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 529,\n                    columnNumber: 19\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 525,\n                  columnNumber: 17\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-1 flex flex-col\",\n                  children: /*#__PURE__*/_jsxDEV(MeetingsList, {\n                    meetings: meetings,\n                    currentMeeting: currentMeeting,\n                    onSelectMeeting: handleSelectMeeting,\n                    onDeleteMeeting: handleDeleteMeeting,\n                    onExportMeeting: () => {},\n                    activeView: \"list\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 536,\n                    columnNumber: 19\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 535,\n                  columnNumber: 17\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 524,\n                columnNumber: 15\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 523,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 414,\n            columnNumber: 11\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 413,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 307,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(GridControls, {\n        onGridSizeChange: setGridSize,\n        onGridRotationChange: setGridRotation,\n        onGridColorChange: setGridColor,\n        currentSize: gridSize,\n        currentRotation: gridRotation,\n        currentColor: gridColor\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 552,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 287,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 286,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"QZGo74Iv2G+R2QYYb+sHjp3F0Ks=\", false, function () {\n  return [useAudioRecorder, useTranscription];\n});\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "useState", "useCallback", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "RecordingPanel", "GridControls", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "List", "jsxDEV", "_jsxDEV", "App", "_s", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "gridSize", "setGridSize", "gridRotation", "setGridRotation", "gridColor", "setGridColor", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "children", "className", "style", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "isRecording", "toLocaleString", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "onGridSizeChange", "onGridRotationChange", "onGridColorChange", "currentSize", "currentRotation", "currentColor", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { \n  Recording<PERSON>utton, \n  RecordingIndicator, \n  TranscriptViewer, \n  MeetingsList, \n  ErrorBoundary, \n  WhisperConfig,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  RecordingPanel,\n  CollapsibleTranscriptsList,\n  GridControls\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List } from 'lucide-react';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  \n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <div \n        className={`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${\n          activeView === 'recording' ? 'grid-page-recording' :\n          activeView === 'transcription' ? 'grid-page-transcription' :\n          activeView === 'transcript' ? 'grid-page-results' :\n          'grid-page-home'\n        }`}\n        style={{\n          '--grid-size': `${gridSize}px`,\n          '--grid-rotation': `${gridRotation}deg`,\n          '--grid-color': gridColor,\n        } as React.CSSProperties}\n      >\n        {/* Static purple overlay layers - no distracting animations */}\n        <div className=\"fixed inset-0 bg-gradient-to-tr from-purple-900/06 via-transparent via-purple-800/04 to-slate-900/08 pointer-events-none\" />\n        <div className=\"fixed inset-0 bg-gradient-to-bl from-transparent via-purple-700/03 via-slate-800/02 to-purple-900/04 pointer-events-none\" />\n        {/* Subtle static purple accent spots */}\n        <div className=\"fixed top-20 left-20 w-96 h-96 bg-gradient-radial from-purple-600/08 to-transparent rounded-full pointer-events-none\" />\n        <div className=\"fixed bottom-20 right-20 w-80 h-80 bg-gradient-radial from-purple-500/06 to-transparent rounded-full pointer-events-none\" />\n        {/* Content wrapper */}\n        <div className=\"relative z-10\">\n          {/* Header */}\n        <header className=\"fixed top-0 left-0 right-0 z-50\">\n          <div className=\"glassmorphism border-b border-white/10\">\n            <div className=\"px-4 sm:px-6 py-2.5\">\n              <div className=\"flex items-center justify-between\">\n                {/* Logo and Title */}\n                <div className=\"flex items-center space-x-2.5\">\n                  <div className=\"w-7 h-7 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-lg flex items-center justify-center shadow-md\">\n                    <Mic2 className=\"h-3.5 w-3.5 text-white drop-shadow-sm\" />\n                  </div>\n                  <div className=\"hidden sm:block\">\n                    <h1 className=\"text-sm font-bold text-white drop-shadow-sm\">MOM App</h1>\n                    <p className=\"text-xs text-white/60 leading-none\">Meeting Recording</p>\n                  </div>\n                  <div className=\"sm:hidden\">\n                    <h1 className=\"text-sm font-bold text-white drop-shadow-sm\">MOM</h1>\n                  </div>\n                </div>\n\n                {/* Center Controls */}\n                <div className=\"flex items-center space-x-2 sm:space-x-4\">\n                  {/* Whisper Status */}\n                  <WhisperStatusIndicator />\n                \n                  {/* Separator - Hidden on mobile */}\n                  <div className=\"hidden sm:block w-px h-6 bg-white/20\"></div>\n                  \n                  {/* Glassmorphism View Switcher */}\n                  <div className=\"flex items-center space-x-1 sm:space-x-2\">\n            <button\n                    onClick={() => setActiveView('recording')}\n                    className={`inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${\n                      activeView === 'recording'\n                        ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105'\n                        : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'\n                    }`}\n                  >\n                    <Mic2 className=\"h-4 w-4 transition-transform duration-200\" />\n                    <span className=\"hidden xs:inline\">Įrašymas</span>\n            </button>\n                  <button\n                    onClick={() => setActiveView('transcription')}\n                    className={`inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${\n                      activeView === 'transcription'\n                        ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105'\n                        : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'\n                    }`}\n                  >\n                    <Zap className=\"h-4 w-4 transition-transform duration-200\" />\n                    <span className=\"hidden xs:inline\">Transkribavimas</span>\n                  </button>\n                  <button\n                    onClick={() => setActiveView('transcript')}\n                    className={`inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300 transform hover:scale-105 ${\n                      activeView === 'transcript'\n                        ? 'bg-gradient-to-r from-white/20 via-white/15 to-white/20 text-white shadow-md backdrop-blur-md border border-white/30 scale-105'\n                        : 'text-white/70 hover:text-white hover:bg-gradient-to-r hover:from-white/15 hover:via-white/10 hover:to-white/15'\n                    }`}\n                  >\n                    <Headphones className=\"h-4 w-4 transition-transform duration-200\" />\n                    <span className=\"hidden xs:inline\">Rezultatai</span>\n                  </button>\n                </div>\n\n                {/* Separator */}\n                <div className=\"w-px h-6 bg-white/20\"></div>\n\n                {/* Action Buttons */}\n                <div className=\"flex items-center space-x-2 sm:space-x-3\">\n                {meetings.length === 0 && (\n                  <button\n                    onClick={loadDemoData}\n                    className=\"inline-flex items-center space-x-1.5 sm:space-x-2 px-3 sm:px-4 py-2 text-sm font-medium text-white/80 hover:text-white bg-gradient-to-r from-amber-500/20 via-orange-500/15 to-amber-500/20 hover:from-amber-500/30 hover:via-orange-500/25 hover:to-amber-500/30 backdrop-blur-md border border-amber-400/30 hover:border-amber-300/40 rounded-lg shadow-md hover:shadow-lg transition-all duration-300\"\n                    title=\"Užkrauti demonstracinius duomenis\"\n                  >\n                    <TestTube className=\"h-4 w-4 text-amber-300\" />\n                    <span className=\"hidden xs:inline\">Demo</span>\n                  </button>\n                )}\n                {/* Glassmorphism Primary Action Button */}\n                <button\n                  onClick={() => {\n                    setActiveView('recording');\n                    // Automatiškai pradėti naują įrašymą\n                    if (!recordingState.isRecording) {\n                      handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                    }\n                  }}\n                  className=\"inline-flex items-center space-x-1.5 sm:space-x-2 px-4 sm:px-5 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\"\n                >\n                  <Plus className=\"h-4 w-4 drop-shadow-sm transition-transform duration-200 group-hover:rotate-90\" />\n                  <span className=\"hidden xs:inline drop-shadow-sm\">Naujas pokalbis</span>\n                </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n        </header>\n\n\n\n\n\n        {/* Main Content */}\n        <main className=\"pt-20 sm:pt-24 md:pt-32 px-3 sm:px-6 md:px-8 pb-4 sm:pb-6 md:pb-8\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 h-[calc(100vh-120px)] sm:h-[calc(100vh-140px)]\">\n            \n            {/* Left Column - Recording/Transcription Management */}\n            <div className=\"lg:col-span-2 space-y-4 sm:space-y-6\">\n              <div className=\"relative h-full\">\n                {/* Recording View */}\n                <div className={`absolute inset-0 transition-all duration-500 ease-in-out ${\n                  activeView === 'recording' \n                    ? 'opacity-100 translate-x-0' \n                    : 'opacity-0 -translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\">\n                    <div className=\"flex items-center justify-between mb-4 sm:mb-6\">\n                      <div className=\"flex items-center space-x-3 sm:space-x-4\">\n                        <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-blue-500/80 to-indigo-600/80 rounded-xl flex items-center justify-center shadow-lg\">\n                          <Mic2 className=\"h-5 w-5 sm:h-6 sm:w-6 text-white\" />\n                        </div>\n                        <div>\n                          <h2 className=\"text-xl sm:text-2xl font-bold text-white\">Pokalbio įrašymas</h2>\n                          <p className=\"text-sm sm:text-base text-white/70\">Pradėkite naują pokalbio įrašymą</p>\n                        </div>\n                      </div>\n                      <button\n                        onClick={() => {\n                          if (!recordingState.isRecording) {\n                            handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                          }\n                        }}\n                        className=\"inline-flex items-center space-x-2 px-4 py-2 text-sm font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-lg shadow-lg hover:shadow-xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 hidden sm:inline-flex\"\n                      >\n                        <Plus className=\"h-4 w-4\" />\n                        <span>Naujas pokalbis</span>\n                      </button>\n                    </div>\n                    \n                    <div className=\"flex-1 flex flex-col\">\n                      <RecordingPanel \n                        recordingState={recordingState}\n                        currentMeeting={currentMeeting}\n                        onStartRecording={handleStartRecording}\n                        onStopRecording={handleStopRecording}\n                        onPauseRecording={() => {}}\n                        onResumeRecording={() => {}}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcription View */}\n                <div className={`absolute inset-0 transition-all duration-500 ease-in-out ${\n                  activeView === 'transcription' \n                    ? 'opacity-100 translate-x-0' \n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\">\n                    <div className=\"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\">\n                      <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-purple-500/80 to-pink-600/80 rounded-xl flex items-center justify-center shadow-lg\">\n                        <Zap className=\"h-5 w-5 sm:h-6 sm:w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl sm:text-2xl font-bold text-white\">Transkribavimas</h2>\n                        <p className=\"text-sm sm:text-base text-white/70\">Audio failų transkribavimas į tekstą</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex-1 flex flex-col\">\n                      <TranscriptionManager \n                        meetings={meetings}\n                        onStartTranscription={handleStartTranscription}\n                        onCancelTranscription={handleCancelTranscription}\n                        isTranscribing={isTranscribing}\n                        currentTranscriptionId={currentTranscriptionId}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onViewResults={() => setActiveView('transcript')}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcript View */}\n                <div className={`absolute inset-0 transition-all duration-500 ease-in-out ${\n                  activeView === 'transcript' \n                    ? 'opacity-100 translate-x-0' \n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\">\n                    <div className=\"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\">\n                      <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-green-500/80 to-emerald-600/80 rounded-xl flex items-center justify-center shadow-lg\">\n                        <Headphones className=\"h-5 w-5 sm:h-6 sm:w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-xl sm:text-2xl font-bold text-white\">Rezultatai</h2>\n                        <p className=\"text-sm sm:text-base text-white/70\">Peržiūrėkite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n                    \n                    <div className=\"flex-1 flex flex-col\">\n                      <ProfessionalTranscriptViewer \n                        meetings={meetings}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onGoToTranscription={() => setActiveView('transcription')}\n                      />\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Right Column - Meetings List */}\n            <div className=\"lg:col-span-1\">\n              <div className=\"glassmorphism rounded-2xl p-4 sm:p-6 h-full flex flex-col animate-fade-in\">\n                <div className=\"flex items-center space-x-3 sm:space-x-4 mb-4 sm:mb-6\">\n                  <div className=\"w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-indigo-500/80 to-purple-600/80 rounded-xl flex items-center justify-center shadow-lg\">\n                    <List className=\"h-5 w-5 sm:h-6 sm:w-6 text-white\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-xl sm:text-2xl font-bold text-white\">Pokalbiai</h2>\n                    <p className=\"text-sm sm:text-base text-white/70\">Visi jūsų pokalbiai</p>\n                  </div>\n                </div>\n                \n                <div className=\"flex-1 flex flex-col\">\n                  <MeetingsList \n                    meetings={meetings}\n                    currentMeeting={currentMeeting}\n                    onSelectMeeting={handleSelectMeeting}\n                    onDeleteMeeting={handleDeleteMeeting}\n                    onExportMeeting={() => {}}\n                    activeView=\"list\"\n                  />\n                </div>\n              </div>\n            </div>\n        </div>\n      </main>\n      </div>\n\n    {/* Grid Controls */}\n    <GridControls\n      onGridSizeChange={setGridSize}\n      onGridRotationChange={setGridRotation}\n      onGridColorChange={setGridColor}\n      currentSize={gridSize}\n      currentRotation={gridRotation}\n      currentColor={gridColor}\n    />\n    </div>\n  </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AACpD,SAIEC,YAAY,EACZC,aAAa,EAEbC,sBAAsB,EACtBC,oBAAoB,EACpBC,4BAA4B,EAC5BC,cAAc,EAEdC,YAAY,QACP,cAAc;AACrB,SAASC,gBAAgB,EAAEC,gBAAgB,QAAQ,SAAS;AAE5D,SAASC,kBAAkB,QAAQ,kBAAkB;AAErD,SAASC,UAAU,EAAEC,IAAI,EAAEC,IAAI,EAAEC,QAAQ,EAAEC,GAAG,EAAYC,IAAI,QAAQ,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAErF,SAASC,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGvB,QAAQ,CAAY,EAAE,CAAC;EACvD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAAiB,IAAI,CAAC;EAC1E,MAAM,CAAC0B,4BAA4B,EAAEC,+BAA+B,CAAC,GAAG3B,QAAQ,CAAiB,IAAI,CAAC;EACtG,MAAM,CAAC4B,UAAU,EAAEC,aAAa,CAAC,GAAG7B,QAAQ,CAA+C,WAAW,CAAC;;EAEvG;EACA,MAAM,CAAC8B,QAAQ,EAAEC,WAAW,CAAC,GAAG/B,QAAQ,CAAC,GAAG,CAAC;EAC7C,MAAM,CAACgC,YAAY,EAAEC,eAAe,CAAC,GAAGjC,QAAQ,CAAC,CAAC,CAAC;EACnD,MAAM,CAACkC,SAAS,EAAEC,YAAY,CAAC,GAAGnC,QAAQ,CAAC,0BAA0B,CAAC;EAEtE,MAAM;IAAEoC,cAAc;IAAEC,cAAc;IAAEC,aAAa;IAAEC,cAAc;IAAEC;EAAgB,CAAC,GAAG/B,gBAAgB,CAAC,CAAC;EAC7G,MAAM;IACJgC,UAAU;IACVC,cAAc;IACdC,uBAAuB;IACvBC,mBAAmB;IACnBC,WAAW;IACXC,eAAe;IACfC,UAAU;IACVC,sBAAsB;IACtBC,QAAQ;IACRC;EACF,CAAC,GAAGxC,gBAAgB,CAAC,CAAC;EAEtB,MAAMyC,oBAAoB,GAAGlD,WAAW,CAAC,MAAOmD,KAAa,IAAK;IAChE,IAAI;MACF,MAAMf,cAAc,CAAC,CAAC;MAEtB,MAAMgB,UAAmB,GAAG;QAC1BC,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBL,KAAK,EAAEA,KAAK;QACZM,IAAI,EAAE,IAAIH,IAAI,CAAC,CAAC;QAChBI,QAAQ,EAAE,CAAC;QACXC,MAAM,EAAE,WAAW;QACnBC,mBAAmB,EAAE;UACnBC,KAAK,EAAE;QACT;MACF,CAAC;MAEDrC,iBAAiB,CAAC4B,UAAU,CAAC;MAC7B9B,WAAW,CAACwC,IAAI,IAAI,CAACV,UAAU,EAAE,GAAGU,IAAI,CAAC,CAAC;MAC1ClC,aAAa,CAAC,WAAW,CAAC;MAC1BiB,eAAe,CAAC,CAAC;IACnB,CAAC,CAAC,OAAOkB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,MAAMA,KAAK;IACb;EACF,CAAC,EAAE,CAAC3B,cAAc,EAAES,eAAe,CAAC,CAAC;EAErC,MAAMoB,mBAAmB,GAAGjE,WAAW,CAAC,YAAY;IAClD,IAAI;MACF,MAAMkE,SAAS,GAAG,MAAM7B,aAAa,CAAC,CAAC;MAEvC,IAAId,cAAc,IAAI2C,SAAS,EAAE;QAC/B,MAAMC,cAAuB,GAAG;UAC9B,GAAG5C,cAAc;UACjBoC,MAAM,EAAE,WAAW;UACnBD,QAAQ,EAAEU,IAAI,CAACC,KAAK,CAAC,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGhC,cAAc,CAACkC,IAAI,CAACa,OAAO,CAAC,CAAC,IAAI,IAAI,CAAC;UACzEJ,SAAS;UACTN,mBAAmB,EAAE;YACnBC,KAAK,EAAE;UACT;QACF,CAAC;QAEDrC,iBAAiB,CAAC2C,cAAc,CAAC;QACjC7C,WAAW,CAACwC,IAAI,IACdA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAK9B,cAAc,CAAC8B,EAAE,GAAGc,cAAc,GAAGK,CAAC,CAC/D,CAAC;;QAED;QACA5C,aAAa,CAAC,eAAe,CAAC;MAChC;IACF,CAAC,CAAC,OAAOmC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpDU,KAAK,CAAC,8BAA8B,CAAC;IACvC;EACF,CAAC,EAAE,CAACpC,aAAa,EAAEd,cAAc,CAAC,CAAC;EAEnC,MAAMmD,wBAAwB,GAAG1E,WAAW,CAAC,MAAO2E,SAAiB,IAAK;IACxE,MAAMC,OAAO,GAAGvD,QAAQ,CAACwD,IAAI,CAACL,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC;IACtD,IAAI,CAACC,OAAO,IAAI,CAACA,OAAO,CAACV,SAAS,EAAE;;IAEpC;IACA,MAAMC,cAAuB,GAAG;MAC9B,GAAGS,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,SAAS;QAChBiB,SAAS,EAAE,IAAIxB,IAAI,CAAC;MACtB;IACF,CAAC;IAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGR,cAAc,GAAGK,CAAC,CAAC,CAAC;IAC3E9C,+BAA+B,CAACyC,cAAc,CAAC;IAE/C,IAAI;MACF;MACA,MAAMY,MAAM,GAAG,MAAMrC,uBAAuB,CAACkC,OAAO,CAACV,SAAS,EAAES,SAAS,EAAE;QACzEK,UAAU,EAAGhC,QAAQ,IAAK;UACxB1B,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAE;cACnB,GAAGY,CAAC,CAACZ,mBAAmB;cACxBZ,QAAQ;cACRa,KAAK,EAAE;YACT;UACF,CAAC,GACDW,CACN,CAAC,CAAC;QACJ,CAAC;QACDS,cAAc,EAAGtB,MAAM,IAAK;UAC1BrC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;YACE,GAAGH,CAAC;YACJZ,mBAAmB,EAAED;UACvB,CAAC,GACDa,CACN,CAAC,CAAC;QACJ,CAAC;QACDU,eAAe,EAAE;MACnB,CAAC,CAAC;;MAEF;MACA,MAAMC,gBAAyB,GAAG;QAChC,GAAGhB,cAAc;QACjB3B,UAAU,EAAEuC,MAAM,CAACK,QAAQ;QAC3BC,YAAY,EAAEN,MAAM,CAACO,QAAQ;QAC7BC,QAAQ,EAAER,MAAM,CAACQ,QAAQ;QACzB3B,mBAAmB,EAAE;UACnBC,KAAK,EAAE,WAAW;UAClBb,QAAQ,EAAE,GAAG;UACb8B,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAAS;UACvDU,WAAW,EAAE,IAAIlC,IAAI,CAAC;QACxB;MACF,CAAC;MAEDhC,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGQ,gBAAgB,GAAGX,CAAC,CAAC,CAAC;MAC7E9C,+BAA+B,CAACyD,gBAAgB,CAAC;;MAEjD;MACAvD,aAAa,CAAC,YAAY,CAAC;MAE3BoC,OAAO,CAACyB,GAAG,CAAC,sCAAsC,EAAE;QAClDL,QAAQ,EAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM;QAChCJ,QAAQ,EAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM;QAChCC,KAAK,EAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU;QACjCC,UAAU,EAAEd,MAAM,CAACQ,QAAQ,CAACO;MAC9B,CAAC,CAAC;IAEJ,CAAC,CAAC,OAAO/B,KAAU,EAAE;MACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAEhD,MAAMgC,YAAqB,GAAG;QAC5B,GAAG5B,cAAc;QACjBP,mBAAmB,EAAE;UACnBC,KAAK,EAAE,QAAQ;UACfE,KAAK,EAAEA,KAAK,CAACiC,OAAO;UACpBlB,SAAS,EAAEX,cAAc,CAACP,mBAAmB,CAACkB;QAChD;MACF,CAAC;MAEDxD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GAAGoB,YAAY,GAAGvB,CAAC,CAAC,CAAC;IAC3E;EACF,CAAC,EAAE,CAACnD,QAAQ,EAAEqB,uBAAuB,CAAC,CAAC;EAEvC,MAAMuD,yBAAyB,GAAGjG,WAAW,CAAE2E,SAAiB,IAAK;IACnEhC,mBAAmB,CAAC,CAAC;IAErBrB,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,IAC5BA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,GACd;MACE,GAAGH,CAAC;MACJZ,mBAAmB,EAAE;QACnB,GAAGY,CAAC,CAACZ,mBAAmB;QACxBC,KAAK,EAAE;MACT;IACF,CAAC,GACDW,CACN,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7B,mBAAmB,CAAC,CAAC;EAEzB,MAAMuD,iBAAiB,GAAGlG,WAAW,CAAC,CAAC2E,SAAiB,EAAEwB,SAAiB,EAAEC,OAAe,KAAK;IAC/FxD,WAAW,CAACuD,SAAS,EAAEC,OAAO,CAAC;;IAE/B;IACA9E,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAACS,GAAG,CAACK,OAAO;MAAA,IAAAyB,mBAAA;MAAA,OAClCzB,OAAO,CAACvB,EAAE,KAAKsB,SAAS,GACpB;QACE,GAAGC,OAAO;QACVpC,UAAU,GAAA6D,mBAAA,GAAEzB,OAAO,CAACpC,UAAU,cAAA6D,mBAAA,uBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,IACzCA,OAAO,CAACjD,EAAE,KAAK8C,SAAS,GACpB;UACE,GAAGG,OAAO;UACVC,IAAI,EAAEH,OAAO;UACbI,QAAQ,EAAE,IAAI;UACdC,QAAQ,EAAE,IAAInD,IAAI,CAAC,CAAC;UACpBoD,QAAQ,EAAE;QACZ,CAAC,GACDJ,OACN;MACF,CAAC,GACD1B,OAAO;IAAA,CACb,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,WAAW,CAAC,CAAC;EAEjB,MAAM+D,mBAAmB,GAAG3G,WAAW,CAAE4E,OAAgB,IAAK;IAC5DpD,iBAAiB,CAACoD,OAAO,CAAC;IAC1B,IAAIA,OAAO,CAACpC,UAAU,IAAIoC,OAAO,CAACpC,UAAU,CAACkD,MAAM,GAAG,CAAC,EAAE;MACvDhE,+BAA+B,CAACkD,OAAO,CAAC;MACxChD,aAAa,CAAC,YAAY,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMgF,mBAAmB,GAAG5G,WAAW,CAAE2E,SAAiB,IAAK;IAC7DrD,WAAW,CAACwC,IAAI,IAAIA,IAAI,CAAC+C,MAAM,CAACrC,CAAC,IAAIA,CAAC,CAACnB,EAAE,KAAKsB,SAAS,CAAC,CAAC;IACzD,IAAI,CAAApD,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE8B,EAAE,MAAKsB,SAAS,EAAE;MACpCnD,iBAAiB,CAAC,IAAI,CAAC;IACzB;IACA,IAAI,CAAAC,4BAA4B,aAA5BA,4BAA4B,uBAA5BA,4BAA4B,CAAE4B,EAAE,MAAKsB,SAAS,EAAE;MAClDjD,+BAA+B,CAAC,IAAI,CAAC;IACvC;EACF,CAAC,EAAE,CAACH,cAAc,EAAEE,4BAA4B,CAAC,CAAC;EAElD,MAAMqF,mBAAmB,GAAG9G,WAAW,CAAE4E,OAAgB,IAAK;IAC5D,MAAMmC,UAAU,GAAG;MACjB5D,KAAK,EAAEyB,OAAO,CAACzB,KAAK;MACpBM,IAAI,EAAEmB,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC;MAChCtD,QAAQ,EAAEkB,OAAO,CAAClB,QAAQ;MAC1BlB,UAAU,EAAEoC,OAAO,CAACpC,UAAU,IAAIA,UAAU;MAC5C6C,YAAY,EAAET,OAAO,CAACS,YAAY,IAAI,EAAE;MACxCE,QAAQ,EAAEX,OAAO,CAACW,QAAQ,IAAI,CAAC,CAAC;MAChC3B,mBAAmB,EAAEgB,OAAO,CAAChB;IAC/B,CAAC;IAED,MAAMqD,OAAO,GAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,EAAE,IAAI,EAAE,CAAC,CAAC;IACnD,MAAMK,OAAO,GAAG,sCAAsC,GAAEC,kBAAkB,CAACJ,OAAO,CAAC;IAEnF,MAAMK,qBAAqB,GAAG,WAAW1C,OAAO,CAACzB,KAAK,CAACoE,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO;IAE5I,MAAMC,WAAW,GAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC;IAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,EAAET,OAAO,CAAC;IACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,EAAEP,qBAAqB,CAAC;IAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC;EACrB,CAAC,EAAE,CAACtF,UAAU,CAAC,CAAC;EAIhB,MAAMuF,YAAY,GAAG/H,WAAW,CAAC,MAAM;IACrC,MAAMgI,YAAY,GAAGtH,kBAAkB,CAAC,CAAC,CAAC6D,GAAG,CAACK,OAAO,KAAK;MACxD,GAAGA,OAAO;MACVhB,mBAAmB,EAAE;QACnBC,KAAK,EAAE,WAAoB;QAC3Bb,QAAQ,EAAE,GAAG;QACbwC,WAAW,EAAEZ,OAAO,CAACnB;MACvB;IACF,CAAC,CAAC,CAAC;IACHnC,WAAW,CAAC0G,YAAY,CAAC;IACzBpG,aAAa,CAAC,YAAY,CAAC;EAC7B,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEV,OAAA,CAAChB,aAAa;IAAA+H,QAAA,eACZ/G,OAAA;MACEgH,SAAS,EAAE,oEACTvG,UAAU,KAAK,WAAW,GAAG,qBAAqB,GAClDA,UAAU,KAAK,eAAe,GAAG,yBAAyB,GAC1DA,UAAU,KAAK,YAAY,GAAG,mBAAmB,GACjD,gBAAgB,EACf;MACHwG,KAAK,EAAE;QACL,aAAa,EAAE,GAAGtG,QAAQ,IAAI;QAC9B,iBAAiB,EAAE,GAAGE,YAAY,KAAK;QACvC,cAAc,EAAEE;MAClB,CAAyB;MAAAgG,QAAA,gBAGzB/G,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC5IrH,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5IrH,OAAA;QAAKgH,SAAS,EAAC;MAAsH;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACxIrH,OAAA;QAAKgH,SAAS,EAAC;MAA0H;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAE5IrH,OAAA;QAAKgH,SAAS,EAAC,eAAe;QAAAD,QAAA,gBAE9B/G,OAAA;UAAQgH,SAAS,EAAC,iCAAiC;UAAAD,QAAA,eACjD/G,OAAA;YAAKgH,SAAS,EAAC,wCAAwC;YAAAD,QAAA,eACrD/G,OAAA;cAAKgH,SAAS,EAAC,qBAAqB;cAAAD,QAAA,eAClC/G,OAAA;gBAAKgH,SAAS,EAAC,mCAAmC;gBAAAD,QAAA,gBAEhD/G,OAAA;kBAAKgH,SAAS,EAAC,+BAA+B;kBAAAD,QAAA,gBAC5C/G,OAAA;oBAAKgH,SAAS,EAAC,mHAAmH;oBAAAD,QAAA,eAChI/G,OAAA,CAACL,IAAI;sBAACqH,SAAS,EAAC;oBAAuC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACvD,CAAC,eACNrH,OAAA;oBAAKgH,SAAS,EAAC,iBAAiB;oBAAAD,QAAA,gBAC9B/G,OAAA;sBAAIgH,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAAO;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACxErH,OAAA;sBAAGgH,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAiB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC,eACNrH,OAAA;oBAAKgH,SAAS,EAAC,WAAW;oBAAAD,QAAA,eACxB/G,OAAA;sBAAIgH,SAAS,EAAC,6CAA6C;sBAAAD,QAAA,EAAC;oBAAG;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACjE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAC,0CAA0C;kBAAAD,QAAA,gBAEvD/G,OAAA,CAACf,sBAAsB;oBAAAiI,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,eAG1BrH,OAAA;oBAAKgH,SAAS,EAAC;kBAAsC;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG5DrH,OAAA;oBAAKgH,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,gBAC/D/G,OAAA;sBACQsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,WAAW,CAAE;sBAC1CsG,SAAS,EAAE,4JACTvG,UAAU,KAAK,WAAW,GACtB,gIAAgI,GAChI,gHAAgH,EACnH;sBAAAsG,QAAA,gBAEH/G,OAAA,CAACL,IAAI;wBAACqH,SAAS,EAAC;sBAA2C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC9DrH,OAAA;wBAAMgH,SAAS,EAAC,kBAAkB;wBAAAD,QAAA,EAAC;sBAAQ;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClD,CAAC,eACHrH,OAAA;sBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,eAAe,CAAE;sBAC9CsG,SAAS,EAAE,4JACTvG,UAAU,KAAK,eAAe,GAC1B,gIAAgI,GAChI,gHAAgH,EACnH;sBAAAsG,QAAA,gBAEH/G,OAAA,CAACH,GAAG;wBAACmH,SAAS,EAAC;sBAA2C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC7DrH,OAAA;wBAAMgH,SAAS,EAAC,kBAAkB;wBAAAD,QAAA,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACnD,CAAC,eACTrH,OAAA;sBACEsH,OAAO,EAAEA,CAAA,KAAM5G,aAAa,CAAC,YAAY,CAAE;sBAC3CsG,SAAS,EAAE,4JACTvG,UAAU,KAAK,YAAY,GACvB,gIAAgI,GAChI,gHAAgH,EACnH;sBAAAsG,QAAA,gBAEH/G,OAAA,CAACP,UAAU;wBAACuH,SAAS,EAAC;sBAA2C;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACpErH,OAAA;wBAAMgH,SAAS,EAAC,kBAAkB;wBAAAD,QAAA,EAAC;sBAAU;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC9C,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACN,CAAC,eAGNrH,OAAA;oBAAKgH,SAAS,EAAC;kBAAsB;oBAAAE,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAG5CrH,OAAA;oBAAKgH,SAAS,EAAC,0CAA0C;oBAAAD,QAAA,GACxD5G,QAAQ,CAACqE,MAAM,KAAK,CAAC,iBACpBxE,OAAA;sBACEsH,OAAO,EAAET,YAAa;sBACtBG,SAAS,EAAC,0YAA0Y;sBACpZ/E,KAAK,EAAC,wCAAmC;sBAAA8E,QAAA,gBAEzC/G,OAAA,CAACJ,QAAQ;wBAACoH,SAAS,EAAC;sBAAwB;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eAC/CrH,OAAA;wBAAMgH,SAAS,EAAC,kBAAkB;wBAAAD,QAAA,EAAC;sBAAI;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxC,CACT,eAEDrH,OAAA;sBACEsH,OAAO,EAAEA,CAAA,KAAM;wBACb5G,aAAa,CAAC,WAAW,CAAC;wBAC1B;wBACA,IAAI,CAACO,cAAc,CAACsG,WAAW,EAAE;0BAC/BvF,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAACoF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;wBACxE;sBACF,CAAE;sBACFR,SAAS,EAAC,4ZAA4Z;sBAAAD,QAAA,gBAEta/G,OAAA,CAACN,IAAI;wBAACsH,SAAS,EAAC;sBAAgF;wBAAAE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE,CAAC,eACnGrH,OAAA;wBAAMgH,SAAS,EAAC,iCAAiC;wBAAAD,QAAA,EAAC;sBAAe;wBAAAG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAM,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAClE,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACJ,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAOTrH,OAAA;UAAMgH,SAAS,EAAC,mEAAmE;UAAAD,QAAA,eACjF/G,OAAA;YAAKgH,SAAS,EAAC,+FAA+F;YAAAD,QAAA,gBAG5G/G,OAAA;cAAKgH,SAAS,EAAC,sCAAsC;cAAAD,QAAA,eACnD/G,OAAA;gBAAKgH,SAAS,EAAC,iBAAiB;gBAAAD,QAAA,gBAE9B/G,OAAA;kBAAKgH,SAAS,EAAE,4DACdvG,UAAU,KAAK,WAAW,GACtB,2BAA2B,GAC3B,8CAA8C,EACjD;kBAAAsG,QAAA,eACD/G,OAAA;oBAAKgH,SAAS,EAAC,2EAA2E;oBAAAD,QAAA,gBACxF/G,OAAA;sBAAKgH,SAAS,EAAC,gDAAgD;sBAAAD,QAAA,gBAC7D/G,OAAA;wBAAKgH,SAAS,EAAC,0CAA0C;wBAAAD,QAAA,gBACvD/G,OAAA;0BAAKgH,SAAS,EAAC,qIAAqI;0BAAAD,QAAA,eAClJ/G,OAAA,CAACL,IAAI;4BAACqH,SAAS,EAAC;0BAAkC;4BAAAE,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAClD,CAAC,eACNrH,OAAA;0BAAA+G,QAAA,gBACE/G,OAAA;4BAAIgH,SAAS,EAAC,0CAA0C;4BAAAD,QAAA,EAAC;0BAAiB;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAC/ErH,OAAA;4BAAGgH,SAAS,EAAC,oCAAoC;4BAAAD,QAAA,EAAC;0BAAgC;4BAAAG,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACnF,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACH,CAAC,eACNrH,OAAA;wBACEsH,OAAO,EAAEA,CAAA,KAAM;0BACb,IAAI,CAACrG,cAAc,CAACsG,WAAW,EAAE;4BAC/BvF,oBAAoB,CAAC,YAAY,IAAII,IAAI,CAAC,CAAC,CAACoF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC;0BACxE;wBACF,CAAE;wBACFR,SAAS,EAAC,iXAAiX;wBAAAD,QAAA,gBAE3X/G,OAAA,CAACN,IAAI;0BAACsH,SAAS,EAAC;wBAAS;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC,eAC5BrH,OAAA;0BAAA+G,QAAA,EAAM;wBAAe;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAM,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtB,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACN,CAAC,eAENrH,OAAA;sBAAKgH,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,eACnC/G,OAAA,CAACZ,cAAc;wBACb6B,cAAc,EAAEA,cAAe;wBAC/BZ,cAAc,EAAEA,cAAe;wBAC/BoH,gBAAgB,EAAEzF,oBAAqB;wBACvC0F,eAAe,EAAE3E,mBAAoB;wBACrC4E,gBAAgB,EAAEA,CAAA,KAAM,CAAC,CAAE;wBAC3BC,iBAAiB,EAAEA,CAAA,KAAM,CAAC;sBAAE;wBAAAV,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC7B;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAE,4DACdvG,UAAU,KAAK,eAAe,GAC1B,2BAA2B,GAC3B,6CAA6C,EAChD;kBAAAsG,QAAA,eACD/G,OAAA;oBAAKgH,SAAS,EAAC,2EAA2E;oBAAAD,QAAA,gBACxF/G,OAAA;sBAAKgH,SAAS,EAAC,uDAAuD;sBAAAD,QAAA,gBACpE/G,OAAA;wBAAKgH,SAAS,EAAC,qIAAqI;wBAAAD,QAAA,eAClJ/G,OAAA,CAACH,GAAG;0BAACmH,SAAS,EAAC;wBAAkC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjD,CAAC,eACNrH,OAAA;wBAAA+G,QAAA,gBACE/G,OAAA;0BAAIgH,SAAS,EAAC,0CAA0C;0BAAAD,QAAA,EAAC;wBAAe;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eAC7ErH,OAAA;0BAAGgH,SAAS,EAAC,oCAAoC;0BAAAD,QAAA,EAAC;wBAAoC;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACvF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENrH,OAAA;sBAAKgH,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,eACnC/G,OAAA,CAACd,oBAAoB;wBACnBiB,QAAQ,EAAEA,QAAS;wBACnB0H,oBAAoB,EAAErE,wBAAyB;wBAC/CsE,qBAAqB,EAAE/C,yBAA0B;wBACjDxD,cAAc,EAAEA,cAAe;wBAC/BM,sBAAsB,EAAEA,sBAAuB;wBAC/CkG,eAAe,EAAErC,mBAAoB;wBACrCsC,aAAa,EAAEA,CAAA,KAAMtH,aAAa,CAAC,YAAY;sBAAE;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAClD;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAGNrH,OAAA;kBAAKgH,SAAS,EAAE,4DACdvG,UAAU,KAAK,YAAY,GACvB,2BAA2B,GAC3B,6CAA6C,EAChD;kBAAAsG,QAAA,eACD/G,OAAA;oBAAKgH,SAAS,EAAC,2EAA2E;oBAAAD,QAAA,gBACxF/G,OAAA;sBAAKgH,SAAS,EAAC,uDAAuD;sBAAAD,QAAA,gBACpE/G,OAAA;wBAAKgH,SAAS,EAAC,uIAAuI;wBAAAD,QAAA,eACpJ/G,OAAA,CAACP,UAAU;0BAACuH,SAAS,EAAC;wBAAkC;0BAAAE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE;sBAAC;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxD,CAAC,eACNrH,OAAA;wBAAA+G,QAAA,gBACE/G,OAAA;0BAAIgH,SAAS,EAAC,0CAA0C;0BAAAD,QAAA,EAAC;wBAAU;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAI,CAAC,eACxErH,OAAA;0BAAGgH,SAAS,EAAC,oCAAoC;0BAAAD,QAAA,EAAC;wBAAsC;0BAAAG,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACzF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC,eAENrH,OAAA;sBAAKgH,SAAS,EAAC,sBAAsB;sBAAAD,QAAA,eACnC/G,OAAA,CAACb,4BAA4B;wBAC3BgB,QAAQ,EAAEA,QAAS;wBACnB4H,eAAe,EAAErC,mBAAoB;wBACrCuC,mBAAmB,EAAEA,CAAA,KAAMvH,aAAa,CAAC,eAAe;sBAAE;wBAAAwG,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3D;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACC,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACH;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGNrH,OAAA;cAAKgH,SAAS,EAAC,eAAe;cAAAD,QAAA,eAC5B/G,OAAA;gBAAKgH,SAAS,EAAC,2EAA2E;gBAAAD,QAAA,gBACxF/G,OAAA;kBAAKgH,SAAS,EAAC,uDAAuD;kBAAAD,QAAA,gBACpE/G,OAAA;oBAAKgH,SAAS,EAAC,uIAAuI;oBAAAD,QAAA,eACpJ/G,OAAA,CAACF,IAAI;sBAACkH,SAAS,EAAC;oBAAkC;sBAAAE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAE;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClD,CAAC,eACNrH,OAAA;oBAAA+G,QAAA,gBACE/G,OAAA;sBAAIgH,SAAS,EAAC,0CAA0C;sBAAAD,QAAA,EAAC;oBAAS;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAI,CAAC,eACvErH,OAAA;sBAAGgH,SAAS,EAAC,oCAAoC;sBAAAD,QAAA,EAAC;oBAAmB;sBAAAG,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eAENrH,OAAA;kBAAKgH,SAAS,EAAC,sBAAsB;kBAAAD,QAAA,eACnC/G,OAAA,CAACjB,YAAY;oBACXoB,QAAQ,EAAEA,QAAS;oBACnBE,cAAc,EAAEA,cAAe;oBAC/B6H,eAAe,EAAEzC,mBAAoB;oBACrCsC,eAAe,EAAErC,mBAAoB;oBACrCyC,eAAe,EAAEA,CAAA,KAAM,CAAC,CAAE;oBAC1B1H,UAAU,EAAC;kBAAM;oBAAAyG,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAClB;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACC,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAGRrH,OAAA,CAACX,YAAY;QACX+I,gBAAgB,EAAExH,WAAY;QAC9ByH,oBAAoB,EAAEvH,eAAgB;QACtCwH,iBAAiB,EAAEtH,YAAa;QAChCuH,WAAW,EAAE5H,QAAS;QACtB6H,eAAe,EAAE3H,YAAa;QAC9B4H,YAAY,EAAE1H;MAAU;QAAAmG,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACzB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACG;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAElB;AAACnH,EAAA,CA7hBQD,GAAG;EAAA,QAWiFX,gBAAgB,EAYvGC,gBAAgB;AAAA;AAAAmJ,EAAA,GAvBbzI,GAAG;AA+hBZ,eAAeA,GAAG;AAAC,IAAAyI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}