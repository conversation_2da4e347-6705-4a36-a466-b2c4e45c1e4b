{"version": 3, "file": "static/css/main.792acca5.css", "mappings": "kGAAA,mDAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,gHAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,mGAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAEd,uBAAmB,CAAnB,gBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,YAAmB,CAAnB,qBAAmB,CAAnB,4BAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,6DAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,oBAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,4BAAmB,CAAnB,sCAAmB,CAAnB,4NAAmB,CAAnB,mCAAmB,CAAnB,oCAAmB,CAAnB,uNAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,yNAAmB,CAAnB,kCAAmB,CAAnB,iBAAmB,CAAnB,mGAAmB,CAAnB,mEAAmB,EAAnB,4CAAmB,CAAnB,8BAAmB,CAAnB,YAAmB,EAAnB,iDAAmB,CAAnB,mDAAmB,EAAnB,+DAAmB,CAAnB,+BAAmB,EAAnB,kEAAmB,CAAnB,+CAAmB,CAAnB,wBAAmB,CAAnB,uCAAmB,CAAnB,+BAAmB,CAAnB,gCAAmB,CAAnB,0DAAmB,CAAnB,0DAAmB,CAAnB,kEAAmB,CAAnB,8GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,8GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,gCAAmB,CAAnB,gCAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,yCAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,8BAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,2CAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,2CAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,2CAAmB,CAAnB,uCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,4CAAmB,CAAnB,4CAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,6CAAmB,CAAnB,6CAAmB,CAAnB,0CAAmB,CAAnB,0CAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,uCAAmB,CAAnB,wCAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,6CAAmB,CAAnB,wCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,0CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,oCAAmB,CAAnB,uCAAmB,CAAnB,oCAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6FAAmB,CAAnB,+EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,gFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,gFAAmB,CAAnB,yDAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,iFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,iFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,iFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,+EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,+EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,yEAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,+EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,iFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,0EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,oEAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,yEAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,6EAAmB,CAAnB,qDAAmB,CAAnB,iEAAmB,CAAnB,kFAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,2EAAmB,CAAnB,2GAAmB,CAAnB,0EAAmB,CAAnB,2GAAmB,CAAnB,0EAAmB,CAAnB,2GAAmB,CAAnB,uEAAmB,CAAnB,yGAAmB,CAAnB,2EAAmB,CAAnB,2GAAmB,CAAnB,uEAAmB,CAAnB,yGAAmB,CAAnB,wEAAmB,CAAnB,yGAAmB,CAAnB,4EAAmB,CAAnB,2GAAmB,CAAnB,6EAAmB,CAAnB,2GAAmB,CAAnB,4EAAmB,CAAnB,2GAAmB,CAAnB,6EAAmB,CAAnB,2GAAmB,CAAnB,sEAAmB,CAAnB,yGAAmB,CAAnB,0EAAmB,CAAnB,2GAAmB,CAAnB,2EAAmB,CAAnB,2GAAmB,CAAnB,oEAAmB,CAAnB,2GAAmB,CAAnB,oEAAmB,CAAnB,uGAAmB,CAAnB,yEAAmB,CAAnB,yGAAmB,CAAnB,oEAAmB,CAAnB,6EAAmB,CAAnB,6EAAmB,CAAnB,uEAAmB,CAAnB,6EAAmB,CAAnB,2EAAmB,CAAnB,2EAAmB,CAAnB,4EAAmB,CAAnB,4EAAmB,CAAnB,sEAAmB,CAAnB,4EAAmB,CAAnB,4EAAmB,CAAnB,sEAAmB,CAAnB,0EAAmB,CAAnB,0EAAmB,CAAnB,0EAAmB,CAAnB,4EAAmB,CAAnB,4EAAmB,CAAnB,sEAAmB,CAAnB,4EAAmB,CAAnB,yEAAmB,CAAnB,yEAAmB,CAAnB,mEAAmB,CAAnB,yEAAmB,CAAnB,2EAAmB,CAAnB,qEAAmB,CAAnB,uEAAmB,CAAnB,uEAAmB,CAAnB,mEAAmB,CAAnB,uEAAmB,CAAnB,4EAAmB,CAAnB,0CAAmB,CAAnB,oBAAmB,CAAnB,+BAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,qBAAmB,CAAnB,mDAAmB,CAAnB,mDAAmB,CAAnB,mDAAmB,CAAnB,2BAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,8GAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,oCAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,6BAAmB,CAAnB,+BAAmB,CAAnB,+CAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,2BAAmB,CAAnB,+BAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,yCAAmB,CAAnB,mDAAmB,CAAnB,4DAAmB,CAAnB,sEAAmB,CAAnB,kGAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,wEAAmB,CAAnB,+FAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,2EAAmB,CAAnB,kGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,wLAAmB,CAAnB,gDAAmB,CAAnB,mTAAmB,CAAnB,sQAAmB,CAAnB,+CAAmB,CAAnB,8CAAmB,CAAnB,kTAAmB,CAAnB,sQAAmB,CAAnB,+CAAmB,CAAnB,qIAAmB,CAAnB,kDAAmB,CAAnB,4EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,2DAAmB,CAMnB,MAEE,0BAA2B,CAC3B,yBAA6B,CAC7B,2BAA4B,CAC5B,wBAA4B,CAC5B,wBAAsC,CAEtC,gCAAiD,CACjD,kCAAmD,CACnD,8BAA6C,CAE7C,yBAA6B,CAC7B,gCAAgD,CAChD,+BAA+C,CAC/C,kCAAkD,CAElD,8BAA+B,CAC/B,4BAA6B,CAC7B,8BAA6C,CAE7C,uBAAwB,CACxB,uBAAwB,CACxB,qBAAsB,CAGtB,sBAAuB,CACvB,uBAAwB,CACxB,qBAAsB,CACtB,uBAAwB,CACxB,sBAAuB,CACvB,sBAAuB,CACvB,wBAAyB,CAGzB,wBAAyB,CACzB,wBAAyB,CACzB,0BAA2B,CAG3B,iBAAkB,CAClB,gBAAiB,CACjB,iBAAkB,CAClB,cAAe,CACf,iBAAkB,CAClB,gBAAiB,CACjB,cAAe,CACf,iBAAkB,CAClB,eAAgB,CAChB,eAAgB,CAGhB,oBAAqB,CACrB,kBAAmB,CACnB,mBAAoB,CACpB,gBAAiB,CAGjB,iCAA4C,CAC5C,6DAAkF,CAClF,+DAAoF,CACpF,iEAAsF,CAGtF,gCAAiC,CACjC,kCAAmC,CACnC,gCACF,CAGA,EAEE,QAAS,CACT,SACF,CAEA,iBALE,qBAQF,CAEA,KACE,cAAe,CACf,sBACF,CAEA,KAYE,kCAAmC,CACnC,iCAAkC,CARlC,oEAKC,CALD,qJAKC,CACD,2BAA4B,CAP5B,UAAgC,CAAhC,+BAAgC,CAHhC,qGAA0H,CAC1H,cAAgC,CAAhC,+BAAgC,CAChC,eAAsC,CAAtC,qCAAsC,CAatC,gBAAiB,CADjB,iBAAkB,CADlB,iCAGF,CAGA,SAAW,6BAA8B,CAAE,gBAAqC,CAArC,oCAAuC,CAClF,SAAW,6BAAwE,CACnF,oBAD2C,eAAsC,CAAtC,qCAC4C,CAAvF,WAAa,+BAA0E,CACvF,SAAW,6BAA8B,CAAE,eAAsC,CAAtC,qCAAwC,CACnF,SAAW,6BAAuE,CAClF,mBAD2C,gBAAqC,CAArC,oCACyC,CAApF,UAAY,8BAAwE,CACpF,UAAY,kBAA+B,CAA/B,8BAA+B,CAAE,gBAAqC,CAArC,oCAAuC,CAEpF,YAAc,eAAkB,CAChC,aAAe,eAAkB,CAKjC,cAAgB,UAAgC,CAAhC,+BAAkC,CAClD,gBAAkB,eAAkC,CAAlC,iCAAoC,CACtD,eAAiB,eAAiC,CAAjC,gCAAmC,CACpD,kBAAoB,eAAoC,CAApC,mCAAsC,CAG1D,iBACE,oEAKC,CALD,qJAKC,CACD,2BAA4B,CAC5B,gBAAiB,CACjB,iBACF,CAEA,YAAc,wBAAyC,CAAzC,wCAA2C,CACzD,cAAgB,qBAA2C,CAA3C,0CAA6C,CAC7D,aAAe,wBAA0C,CAA1C,yCAA4C,CAC3D,aAAe,qBAA0C,CAA1C,yCAA4C,CAG3D,MACE,eAAoC,CAApC,mCAAoC,CACpC,0BAA6C,CAA7C,4CAA6C,CAC7C,8BAA+B,CAC/B,4DAA4B,CAA5B,2BAA4B,CAC5B,4BAAwC,CAAxC,uCACF,CAEA,YACE,sBAA2C,CAA3C,0CAA2C,CAC3C,8DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,cACE,eAAqC,CAArC,oCAAqC,CACrC,0BAA+C,CAA/C,8CAA+C,CAC/C,mBAA+B,CAA/B,8BAA+B,CAC/B,gCAA4B,CAA5B,2BAA4B,CAC5B,4BAAsC,CAAtC,qCACF,CAGA,eAEE,0BAA2B,CAC3B,kCAAmC,CAFnC,oBAAqC,CAGrC,0BAA6C,CAA7C,4CAA6C,CAC7C,sFAEwC,CAFxC,mDAGF,CAGA,cACE,gBACF,CAyBA,qBACE,mBACF,CAaA,6BAJE,iBAOF,CAHA,iBAEE,SACF,CAoCA,aACE,6CACF,CAGA,eACE,iBACF,CAEA,eACE,kBACF,CAEA,eACE,gBACF,CAEA,eACE,eACF,CAEA,eACE,iBACF,CAiHA,SACE,gBAAkB,CAClB,gBACF,CAEA,SACE,iBAAmB,CACnB,mBACF,CAEA,WACE,cAAe,CACf,kBACF,CAEA,SACE,kBAEF,CAEA,kBAHE,mBAMF,CAHA,SACE,iBAEF,CAEA,UACE,gBAAiB,CACjB,gBACF,CAEA,aACE,eACF,CAEA,eACE,eACF,CAEA,WACE,eACF,CAGA,YACE,UACF,CAEA,gBACE,eACF,CAEA,gBACE,WACF,CAGA,cACE,0BACF,CAEA,cACE,sBACF,CAGA,QACE,gBACF,CAEA,UACE,uBACF,CAEA,kBACE,sBACF,CAEA,kBACE,kBACF,CAEA,kBACE,sBACF,CAGA,SACE,oBACF,CAEA,YACE,mBACF,CAEA,YACE,oBACF,CAEA,aACE,kBACF,CAEA,cACE,oBACF,CAGA,WACE,8DACF,CAEA,WACE,+DACF,CAEA,WACE,4DACF,CAGA,kBACE,kCAA2B,CAA3B,0BACF,CAGA,gBAGE,wBAA0B,CAF1B,uBAAwB,CACxB,kDAEF,CAEA,cACE,uBACF,CAEA,cACE,uBACF,CAEA,aACE,kDACF,CAOA,sBAHE,6LAOF,CAJA,WACE,iBAAkB,CAClB,iBAEF,CAEA,UAGE,6LACF,CASA,mCAZE,iBAAkB,CAClB,iBAeF,CAGA,kBACE,mEACF,CAEA,mBACE,uEACF,CAEA,mBACE,yEACF,CAEA,oBACE,iEACF,CAGA,qBACE,4BAAyC,CACzC,0BAAoC,CACpC,iEACF,CAEA,iBACE,sBAAsC,CACtC,uEACF,CAEA,oBACE,0BAAqC,CACrC,2EACF,CAEA,kBACE,0BACF,CAEA,eACE,0BAA2B,CAC3B,0BAAqC,CACrC,iEACF,CAEA,eACE,wBACF,CAEA,mBACE,4BAAyC,CACzC,0BAAqC,CACrC,iEACF,CAEA,kBACE,0BAAoC,CACpC,2EACF,CAEA,mBACE,0BACF,CAEA,gCACE,4BAAyC,CACzC,0BAEF,CAEA,+BACE,0BAAoC,CACpC,2EACF,CAEA,gCACE,0BACF,CAGA,OAEE,WAAY,CADZ,cAAe,CAGf,mBAAoB,CADpB,YAEF,CAEA,gBAEE,kBAAmB,CADnB,UAEF,CAGA,KASE,sBAA6B,CAD7B,0CAEF,CAEA,WACE,qBACF,CAEA,YACE,oBACF,CAGA,aAGE,kCAA2B,CAA3B,0BAA2B,CAD3B,+DAAwG,CAExG,sBAAmC,CACnC,8DACF,CAEA,mBACE,+DAAwG,CACxG,sBAAmC,CACnC,+DACF,CAGA,qBACE,iBACF,CAEA,4BAYE,6CAA8C,CAL9C,wIAEoF,CAEpF,iCAAmC,CADnC,qCAAuC,CAJvC,QAAS,CALT,UAAW,CAGX,MAAO,CASP,mBAAoB,CAXpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAWN,SACF,CAEA,yBACE,iBACF,CAEA,gCAYE,6CAA8C,CAL9C,wIAEoF,CAEpF,iCAAmC,CADnC,qCAAuC,CAJvC,QAAS,CALT,UAAW,CAGX,MAAO,CASP,mBAAoB,CAXpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAWN,SACF,CAEA,mBACE,iBACF,CAEA,0BAYE,4CAA6C,CAL7C,wIAEoF,CAEpF,iCAAmC,CADnC,qCAAuC,CAJvC,QAAS,CALT,UAAW,CAGX,MAAO,CASP,mBAAoB,CAXpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAWN,SACF,CAEA,gBACE,iBACF,CAEA,uBAOE,wIAEmF,CAEnF,iCAAmC,CADnC,qCAAuC,CAJvC,QAAS,CALT,UAAW,CAGX,MAAO,CAQP,mBAAoB,CAVpB,iBAAkB,CAGlB,OAAQ,CAFR,KAAM,CAUN,SACF,CAGA,sBACE,MACE,uBAAwB,CACxB,UACF,CACA,IACE,6BAA8B,CAC9B,SACF,CACF,CAEA,sBACE,MACE,uBAAwB,CACxB,UACF,CACA,IACE,0BAA2B,CAC3B,UACF,CACA,IACE,8BAA+B,CAC/B,UACF,CACF,CAEA,qBACE,MACE,uBAAwB,CACxB,UACF,CACA,IACE,4BAA6B,CAC7B,UACF,CACA,IACE,6BAA8B,CAC9B,UACF,CACF,CAGA,mBACE,GACE,SACF,CACA,GACE,SACF,CACF,CAEA,sBACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,wBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,yBACE,GACE,SAAU,CACV,2BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,0BACE,GACE,SAAU,CACV,0BACF,CACA,GACE,SAAU,CACV,uBACF,CACF,CAEA,oBACE,GACE,SAAU,CACV,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAEA,qBACE,GACE,SAAU,CACV,mBACF,CACA,IACE,SAAU,CACV,qBACF,CACA,IACE,mBACF,CACA,GACE,SAAU,CACV,kBACF,CACF,CAGA,iBACE,8BACF,CAEA,oBACE,iCACF,CAEA,sBACE,mCACF,CAEA,uBACE,oCACF,CAEA,wBACE,qCACF,CAEA,kBACE,+BACF,CAEA,mBACE,gCACF,CAGA,qBACE,mBACF,CAEA,qBACE,mBACF,CAEA,qBACE,mBACF,CAEA,qBACE,mBACF,CAEA,qBACE,mBACF,CAGA,kBACE,0CACF,CAEA,mBACE,0CACF,CAGA,eACE,oBAAqC,CAGrC,0BAA2C,CAC3C,mDAGF,CAGA,qCATE,0BAA2B,CAC3B,kCAgBF,CARA,sBACE,oBAAqC,CAGrC,0BAA2C,CAC3C,uDAGF,CAGA,sBAEE,0BAA2B,CAC3B,kCAAmC,CAFnC,gBAA8B,CAG9B,iCAAiD,CACjD,uDAEwC,CACxC,0CACF,CAGA,wBAME,kBAAmB,CAHnB,8DAA0E,CAC1E,kBAAmB,CAInB,oDAEoC,CAEpC,cAAe,CAPf,YAAa,CAHb,WAAY,CAKZ,sBAAuB,CAOvB,eAAgB,CADhB,iBAAkB,CAFlB,0CAAiD,CAVjD,UAcF,CAEA,+BAIE,kDAAiF,CACjF,qBAAsB,CAJtB,UAAW,CAEX,OAAQ,CAGR,SAAU,CAJV,iBAAkB,CAKlB,2BACF,CAEA,qCACE,SACF,CAEA,8BAEE,qDAEoC,CAHpC,sCAIF,CAGA,uBACE,iBACF,CAEA,mBAEE,kBAAmB,CAInB,0BAA2B,CAC3B,kCAAmC,CAFnC,oBAAqC,CAGrC,0BAA0C,CAC1C,kBAAmB,CACnB,mDAEwC,CAXxC,YAAa,CAEb,OAAQ,CACR,WASF,CAGA,kBAEE,kBAAmB,CAOnB,gBAAuB,CACvB,WAAY,CALZ,kBAAmB,CAGnB,eAA+B,CAG/B,cAAe,CAVf,YAAa,CAKb,cAAe,CACf,eAAgB,CAJhB,OAAQ,CAWR,eAAgB,CAVhB,iBAAkB,CASlB,iBAAkB,CADlB,0CAGF,CAEA,yBAIE,sDAA6F,CAC7F,qBAAsB,CAJtB,UAAW,CAEX,OAAQ,CAGR,SAAU,CAJV,iBAAkB,CAKlB,2BACF,CAEA,+BACE,SACF,CAEA,wBACE,eAA+B,CAC/B,0BACF,CAEA,yBAEE,sDAA6F,CAC7F,mDAEmC,CAJnC,UAAY,CAKZ,0BACF,CAEA,gCACE,SACF,CAGA,0BACE,iBACF,CAGA,wBAEE,kBAAmB,CAInB,0BAA2B,CAC3B,kCAAmC,CAFnC,oBAAmC,CAGnC,0BAAyC,CACzC,kBAAmB,CARnB,mBAAoB,CAEpB,OAAQ,CASR,eAAgB,CARhB,gBAAiB,CAOjB,iBAAkB,CADlB,0CAGF,CAEA,+BAIE,sDAA8F,CAC9F,qBAAsB,CAJtB,UAAW,CAEX,OAAQ,CAGR,SAAU,CAJV,iBAAkB,CAKlB,2BACF,CAEA,qCACE,SACF,CAGA,oBAGE,iBAAkB,CADlB,UAAW,CAGX,iBAAkB,CADlB,0CAAiD,CAHjD,SAKF,CAEA,8BAKE,gCAAiC,CAJjC,kDAA6D,CAC7D,gDAIF,CAEA,iCAKE,gCAAiC,CAJjC,kDAA6D,CAC7D,gDAIF,CAEA,sBACE,MAEE,SAAU,CADV,kBAEF,CACA,IAEE,UAAY,CADZ,oBAEF,CACF,CAGA,qBAEE,kBAAmB,CAInB,0BAA2B,CAC3B,kCAAmC,CAFnC,oBAAmC,CAGnC,0BAAyC,CACzC,kBAAmB,CACnB,aAAwB,CAGxB,cAAe,CAZf,YAAa,CAUb,cAAe,CACf,eAAgB,CAThB,OAAQ,CAaR,eAAgB,CAZhB,iBAAkB,CAWlB,iBAAkB,CADlB,0CAGF,CAEA,4BAIE,sDAA6F,CAC7F,qBAAsB,CAJtB,UAAW,CAEX,OAAQ,CAGR,SAAU,CAJV,iBAAkB,CAKlB,2BACF,CAEA,kCACE,SACF,CAEA,2BACE,oBAAoC,CACpC,sBAAqC,CAGrC,oDAEoC,CAJpC,aAAwB,CACxB,0BAIF,CAGA,wBAEE,kBAAmB,CAGnB,8DAA0E,CAC1E,WAAY,CACZ,kBAAmB,CAQnB,oDAEoC,CATpC,UAAY,CAGZ,cAAe,CAVf,YAAa,CAQb,cAAe,CACf,eAAgB,CAPhB,OAAQ,CAWR,eAAgB,CAVhB,iBAAkB,CASlB,iBAAkB,CADlB,0CAMF,CAEA,+BAIE,kDAAiF,CACjF,qBAAsB,CAJtB,UAAW,CAEX,OAAQ,CAGR,SAAU,CAJV,iBAAkB,CAKlB,2BACF,CAEA,qCACE,SACF,CAEA,8BAEE,qDAEoC,CAHpC,sCAIF,CAGA,yBACE,mBACE,OAAQ,CACR,WACF,CAEA,kBAEE,cAAe,CADf,gBAEF,CAEA,wBAEE,WAAY,CADZ,UAEF,CAEA,wBAEE,cAAe,CADf,iBAEF,CAEA,qBAEE,cAAe,CADf,gBAEF,CAEA,wBAEE,cAAe,CADf,gBAEF,CACF,CAEA,yBACE,sBACE,0BAA2B,CAC3B,kCACF,CAEA,mBACE,WACF,CAEA,kBAEE,cAAe,CADf,eAEF,CAEA,wBAEE,WAAY,CADZ,UAEF,CACF,CAGA,6BACE,GAEE,SAAU,CADV,2BAEF,CACA,GAEE,SAAU,CADV,uBAEF,CACF,CAEA,sBACE,uDACF,CAGA,gDACE,wDAGF,CAGA,yGAGE,2BAA0C,CAC1C,kBACF,CAGA,WACE,2BACF,CAEA,iBACE,kCACF,CAGA,sBACE,sDACF,CAEA,sBACE,sDACF,CAEA,sBACE,sDACF,CAGA,gBACE,qDACF,CAEA,gBACE,qDACF,CAEA,aACE,+BACF,CAGA,sBACE,8BACF,CAEA,4BACE,sDACF,CAEA,cACE,iDACF,CAEA,oBAEE,gCAA2C,CAD3C,0BAEF,CAGA,cACE,mDACF,CAGA,sBAEE,sDAAgG,CAChG,kBAAmB,CAFnB,iBAGF,CAEA,6BAKE,sDAA4F,CAC5F,qBAAsB,CALtB,UAAW,CAEX,OAAQ,CAIR,4EAAsE,CAAtE,oEAAsE,CACtE,0BAAuB,CAAvB,sBAAuB,CAJvB,WAAY,CAFZ,iBAOF,CAIE,8BAAmH,CAAnH,mBAAmH,CAAnH,oBAAmH,CAAnH,8BAAmH,CAAnH,mBAAmH,CAAnH,6BAAmH,CAAnH,iBAAmH,CAAnH,eAAmH,CAAnH,sBAAmH,CAAnH,eAAmH,CAAnH,qCAAmH,CAAnH,mBAAmH,CAAnH,kBAAmH,CAAnH,qCAAmH,CAAnH,uBAAmH,CAAnH,uBAAmH,CAAnH,kDAAmH,CAAnH,4BAAmH,CAAnH,qCAAmH,CAInH,iFAA4N,CAA5N,yDAA4N,CAA5N,mBAA4N,CAA5N,6BAA4N,CAA5N,gVAA4N,CAA5N,sQAA4N,CAA5N,kCAA4N,CAA5N,0BAA4N,CAA5N,mEAA4N,CAA5N,sBAA4N,CAA5N,gBAA4N,CAA5N,+CAA4N,CAA5N,UAA4N,CAA5N,4cAA4N,EAA5N,sDAA4N,CAA5N,0BAA4N,CAA5N,iEAA4N,CAA5N,sBAA4N,CAA5N,mFAA4N,CAA5N,2EAA4N,CAA5N,0BAA4N,CAA5N,6DAA4N,CAA5N,yDAA4N,CAA5N,yDAA4N,CAA5N,2GAA4N,CAA5N,yDAA4N,EAI5N,qEAAgB,CAAhB,0GAAgB,CAAhB,sCAAgB,CAIhB,8CAAwC,CAAxC,cAAwC,CAAxC,eAAwC,CAAxC,eAAwC,CAAxC,qCAAwC,CAAxC,kBAAwC,CAAxC,qBAAwC,CAAxC,qCAAwC,CAI1C,yBACE,iBACE,iBACF,CACF,CAEA,yBACE,iBACE,cACF,CACF,CAGA,oBACE,SACF,CAEA,0BACE,oBAAoC,CACpC,iBACF,CAEA,0BACE,oBAAoC,CACpC,iBACF,CAEA,gCACE,oBACF,CAIE,4HAA+G,CAA/G,wGAA+G,CAA/G,yBAA+G,CAA/G,0BAA+G,CAA/G,oGAA+G,CAA/G,wFAA+G,CAA/G,uBAA+G,CAA/G,kBAA+G,CAIjH,iBAGE,+BAAgC,CAFhC,uEAA4H,CAC5H,yBAEF,CAYA,oBACE,2DACF,CAGA,sBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA2C,CAC3C,gCACF,CAEA,sBAEE,kCAA2B,CAA3B,0BAA2B,CAD3B,oBAAqC,CAErC,0BAA0C,CAC1C,+BACF,CAGA,gBACE,0CACF,CAEA,sBAEE,gCAA2C,CAD3C,0BAEF,CAGA,uBAGE,6BAAoC,CAFpC,kDAA6D,CAC7D,4BAA6B,CAE7B,oBACF,CAGA,kBAGE,2BAA4B,CAF5B,kDAA6D,CAC7D,6BAEF,CAEA,mBAGE,2BAA4B,CAF5B,kDAA6D,CAC7D,6BAEF,CAEA,iBACE,kDAA6D,CAC7D,6BACF,CAMA,yBACE,MACE,yBAA0B,CAC1B,iBAAkB,CAClB,cACF,CAEA,YAAc,sBAA0B,CACxC,WAAa,uBAA2B,CACxC,UAAY,sBAA0B,CACtC,UAAY,sBAA0B,CACtC,aAAe,0BAAyC,CAAzC,uCAA2C,CAC1D,aAAe,2BAAyC,CAAzC,uCAA2C,CAC1D,SAAW,uBAAkC,CAAlC,gCAAoC,CAC/C,SAAW,wBAAkC,CAAlC,gCAAoC,CAC/C,WAAa,mBAA8B,CAA9B,4BAAgC,CAC7C,WAAa,oBAA8B,CAA9B,4BAAgC,CAC/C,CAGA,yBACE,YAAc,sBAA0B,CACxC,WAAa,uBAA2B,CACxC,UAAY,sBAA0B,CACtC,UAAY,sBAA0B,CACtC,aAAe,2BAAyC,CAAzC,uCAA2C,CAC1D,eAAiB,wBAA2C,CAA3C,yCAA6C,CAC9D,aAAe,4BAAyC,CAAzC,uCAA2C,CAC1D,SAAW,sBAAkC,CAAlC,gCAAoC,CAC/C,SAAW,wBAAkC,CAAlC,gCAAoC,CAC/C,WAAa,kBAA8B,CAA9B,4BAAgC,CAC7C,WAAa,oBAA8B,CAA9B,4BAAgC,CAC/C,CAGA,yBACE,YAAc,sBAA0B,CACxC,WAAa,uBAA2B,CACxC,UAAY,sBAA0B,CACtC,UAAY,sBAA0B,CACtC,eAAiB,wBAA2C,CAA3C,yCAA6C,CAC9D,aAAe,4BAAyC,CAAzC,uCAA2C,CAC1D,aAAe,2BAAyC,CAAzC,uCAA2C,CAC1D,SAAW,wBAAkC,CAAlC,gCAAoC,CAC/C,SAAW,sBAAkC,CAAlC,gCAAoC,CAC/C,WAAa,oBAA8B,CAA9B,4BAAgC,CAC7C,WAAa,kBAA8B,CAA9B,4BAAgC,CAC/C,CAGA,0BACE,YAAc,sBAA0B,CACxC,WAAa,uBAA2B,CACxC,UAAY,sBAA0B,CACtC,UAAY,sBAA0B,CACtC,aAAe,4BAAyC,CAAzC,uCAA2C,CAC1D,aAAe,2BAAyC,CAAzC,uCAA2C,CAC1D,cAAgB,0BAA0C,CAA1C,wCAA4C,CAC5D,SAAW,sBAAkC,CAAlC,gCAAoC,CAC/C,UAAY,sBAAmC,CAAnC,iCAAqC,CACjD,WAAa,kBAA8B,CAA9B,4BAAgC,CAC7C,YAAc,kBAA+B,CAA/B,6BAAiC,CACjD,CAGA,0BACE,YAAc,sBAA0B,CACxC,WAAa,uBAA2B,CACxC,UAAY,sBAA0B,CACtC,UAAY,sBAA0B,CACtC,aAAe,2BAAyC,CAAzC,uCAA2C,CAC1D,cAAgB,0BAA0C,CAA1C,wCAA4C,CAC5D,cAAgB,4BAA0C,CAA1C,wCAA4C,CAC5D,UAAY,sBAAmC,CAAnC,iCAAqC,CACjD,UAAY,sBAAmC,CAAnC,iCAAqC,CACjD,YAAc,kBAA+B,CAA/B,6BAAiC,CAC/C,YAAc,kBAA+B,CAA/B,6BAAiC,CACjD,CAGA,KAEE,kBAAmB,CAOnB,WAAY,CACZ,mBAA+B,CAA/B,8BAA+B,CAC/B,cAAe,CAVf,mBAAoB,CAKpB,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAHhB,SAAmB,CAAnB,kBAAmB,CADnB,sBAAuB,CAKvB,aAAc,CAQd,YAAa,CAXb,kBAAsC,CAAtC,qCAAsC,CAQtC,oBAAqB,CADrB,4BAAsC,CAAtC,qCAAsC,CAGtC,wBAAiB,CAAjB,gBAAiB,CADjB,kBAGF,CAEA,mBACE,yBAA8C,CAA9C,6CAA8C,CAC9C,kBACF,CAEA,cAEE,kBAAmB,CADnB,UAAY,CAEZ,mBACF,CAGA,aACE,kBAAuC,CAAvC,sCAAuC,CAEvC,gCAA4B,CAA5B,2BAA4B,CAD5B,UAEF,CAEA,kCACE,kBAAqC,CAArC,oCAAqC,CACrC,4DAA4B,CAA5B,2BAA4B,CAC5B,0BACF,CAEA,eACE,eAAoC,CAApC,mCAAoC,CAEpC,0BAA6C,CAA7C,4CAA6C,CAD7C,UAAgC,CAAhC,+BAEF,CAEA,oCACE,kBAAoC,CAApC,mCAAoC,CACpC,sBAA2C,CAA3C,0CAA2C,CAC3C,0BACF,CAEA,WACE,gBAAuB,CACvB,eAAkC,CAAlC,iCACF,CAEA,gCACE,eAAoC,CAApC,mCAAoC,CACpC,UAAgC,CAAhC,+BACF,CAGA,QAEE,gBAA8B,CAA9B,6BAA8B,CAC9B,UAAmB,CAAnB,kBAAmB,CAFnB,qBAAsC,CAAtC,qCAGF,CAEA,QAEE,cAAgC,CAAhC,+BAAgC,CAChC,UAAmB,CAAnB,kBAAmB,CAFnB,qBAAsC,CAAtC,qCAGF,CAGA,UAGE,aAAc,CAFd,aAAuB,CAAvB,sBAAuB,CACvB,YAEF,CAEA,iBAGE,WAAY,CAFZ,cAAuB,CAAvB,sBAAuB,CACvB,UAEF,CAIE,qEAA+E,CAA/E,iGAA+E,CAA/E,6BAA+E,CAA/E,8QAA+E,CAA/E,sQAA+E,CAA/E,kCAA+E,CAA/E,0BAA+E,CAA/E,0BAA+E,CAA/E,kBAA+E,CAA/E,oBAA+E,CAA/E,kBAA+E,CAA/E,8BAA+E,CAA/E,gBAA+E,CAA/E,+CAA+E,CAA/E,kGAA+E,CAA/E,8DAA+E,CAA/E,8DAA+E,CAA/E,2BAA+E,CAI/E,oCAAyC,CAAzC,sBAAyC,CAQzC,qCARA,mBAAyC,CAAzC,qCAQyC,CAAzC,mCAAyC,CAAzC,oBAAyC,CAKzC,kCAAsM,CAAtM,+CAAsM,CAAtM,2CAAsM,CAAtM,4CAAsM,CAAtM,8QAAsM,CAAtM,sQAAsM,CAAtM,kCAAsM,CAAtM,0BAAsM,CAAtM,0BAAsM,CAAtM,kBAAsM,CAAtM,mBAAsM,CAAtM,oBAAsM,CAAtM,+CAAsM,CAAtM,UAAsM,CAAtM,kBAAsM,CAAtM,qCAAsM,CAAtM,UAAsM,CAAtM,+HAAsM,CAAtM,wGAAsM,CAAtM,gDAAsM,CAAtM,uEAAsM,CAAtM,wFAAsM,CAAtM,uBAAsM,CAAtM,kBAAsM,CAKtM,6BAA6F,CAA7F,0CAA6F,EAA7F,8BAA6F,CAA7F,iCAA6F,CAA7F,sBAA6F,CAA7F,oBAA6F,CAA7F,gBAA6F,CAA7F,sBAA6F,CAI/F,SAEE,oBAAqB,CADrB,iBAEF,CAEA,gBAiBE,kCAA2B,CAA3B,0BAA2B,CAX3B,gBAAmC,CAAnC,kCAAmC,CAYnC,0BAA6C,CAA7C,4CAA6C,CAT7C,mBAA+B,CAA/B,8BAA+B,CAU/B,8DAA4B,CAA5B,2BAA4B,CAZ5B,UAAgC,CAAhC,+BAAgC,CANhC,0BAA2B,CAS3B,gBAA8B,CAA9B,6BAA8B,CAC9B,eAAgB,CAHhB,oBAAsC,CAAtC,qCAAsC,CAHtC,2CAA4C,CAO5C,kBAQF,CAEA,+BAnBE,WAAY,CACZ,QAAS,CAST,SAAU,CAXV,iBAAkB,CAalB,4BAAsC,CAAtC,qCAAsC,CADtC,iBAAkB,CAElB,YAkBF,CAZA,eAOE,sBAAyC,CAAzC,4CAAyC,CANzC,UAAW,CAIX,2CAOF,CAEA,2CAEE,SAAU,CAEV,2CAA4C,CAD5C,kBAEF,CAGA,OAGE,eAAqC,CAArC,oCAAqC,CACrC,0BAA6C,CAA7C,4CAA6C,CAC7C,mBAA+B,CAA/B,8BAA+B,CAC/B,UAAgC,CAAhC,+BAAgC,CAChC,iBAA8B,CAA9B,6BAA8B,CAC9B,eAAsC,CAAtC,qCAAsC,CAEtC,YAAa,CARb,mBAAsC,CAAtC,qCAAsC,CAOtC,4BAAsC,CAAtC,qCAAsC,CARtC,UAUF,CAEA,oBACE,eAAoC,CAApC,mCACF,CAEA,aAGE,kBAAoC,CAApC,mCAAoC,CAFpC,oBAAyC,CAAzC,wCAAyC,CACzC,8BAA+C,CAA/C,8CAEF,CAEA,gBAGE,kBAAmC,CAAnC,kCAAmC,CADnC,kBAAmB,CADnB,UAGF,CAGA,UAEE,gBAA8B,CAA9B,6BAA8B,CAD9B,oBAAsC,CAAtC,qCAEF,CAEA,UAEE,cAAgC,CAAhC,+BAAgC,CADhC,oBAAsC,CAAtC,qCAEF,CAGA,UAGE,kBAAoC,CAApC,mCAAoC,CADpC,UAAW,CAGX,eAAgB,CAJhB,UAMF,CAEA,yBALE,qBAA+B,CAA/B,8BAA+B,CAE/B,iBASF,CANA,eAEE,iDAA0F,CAA1F,uFAA0F,CAD1F,WAAY,CAGZ,8BAA0C,CAA1C,yCAEF,CAEA,qBAQE,6BAA8B,CAD9B,mDAAsF,CADtF,QAAS,CALT,UAAW,CAGX,MAAO,CAFP,iBAAkB,CAGlB,OAAQ,CAFR,KAMF,CAEA,mBACE,GAAK,2BAA8B,CACnC,GAAO,0BAA6B,CACtC,CAGA,MAAQ,YAAe,CACvB,aAAe,mBAAsB,CACrC,MAAQ,YAAe,CACvB,QAAU,YAAe,CACzB,OAAS,aAAgB,CAEzB,UAAY,qBAAwB,CACpC,UAAY,kBAAqB,CACjC,cAAgB,kBAAqB,CACrC,aAAe,sBAAyB,CACxC,WAAa,oBAAuB,CACpC,gBAAkB,sBAAyB,CAC3C,iBAAmB,6BAAgC,CACnD,eAAiB,0BAA6B,CAC9C,aAAe,wBAA2B,CAE1C,QAAU,QAAc,CACxB,WAAa,SAAY,CAGzB,OAAS,UAAmB,CAAnB,kBAAqB,CAC9B,OAAS,SAAmB,CAAnB,kBAAqB,CAC9B,OAAS,UAAmB,CAAnB,kBAAqB,CAC9B,OAAS,QAAmB,CAAnB,kBAAqB,CAC9B,OAAS,WAAmB,CAAnB,kBAAqB,CAC9B,OAAS,UAAmB,CAAnB,kBAAqB,CAC9B,OAAS,QAAmB,CAAnB,kBAAqB,CAE9B,KAAO,cAAuB,CAAvB,sBAAyB,CAChC,KAAO,aAAuB,CAAvB,sBAAyB,CAChC,KAAO,cAAuB,CAAvB,sBAAyB,CAChC,KAAO,YAAuB,CAAvB,sBAAyB,CAChC,KAAO,eAAuB,CAAvB,sBAAyB,CAChC,KAAO,cAAuB,CAAvB,sBAAyB,CAChC,KAAO,YAAuB,CAAvB,sBAAyB,CAEhC,MAAQ,mBAA4B,CAA5B,2BAA4B,CAAE,oBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,kBAA4B,CAA5B,2BAA4B,CAAE,mBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,mBAA4B,CAA5B,2BAA4B,CAAE,oBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,iBAA4B,CAA5B,2BAA4B,CAAE,kBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,oBAA4B,CAA5B,2BAA4B,CAAE,qBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,mBAA4B,CAA5B,2BAA4B,CAAE,oBAA6B,CAA7B,4BAA+B,CACrE,MAAQ,iBAA4B,CAA5B,2BAA4B,CAAE,kBAA6B,CAA7B,4BAA+B,CAErE,MAAqC,qBAA8B,CAA9B,6BAA8B,CAA3D,kBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,oBAA8B,CAA9B,6BAA8B,CAA3D,iBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,qBAA8B,CAA9B,6BAA8B,CAA3D,kBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,mBAA8B,CAA9B,6BAA8B,CAA3D,gBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,sBAA8B,CAA9B,6BAA8B,CAA3D,mBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,qBAA8B,CAA9B,6BAA8B,CAA3D,kBAA2B,CAA3B,0BAA6D,CACrE,MAAqC,mBAA8B,CAA9B,6BAA8B,CAA3D,gBAA2B,CAA3B,0BAA6D,CAErE,KAAO,aAAsB,CAAtB,qBAAwB,CAC/B,KAAO,YAAsB,CAAtB,qBAAwB,CAC/B,KAAO,aAAsB,CAAtB,qBAAwB,CAC/B,KAAO,WAAsB,CAAtB,qBAAwB,CAC/B,KAAO,cAAsB,CAAtB,qBAAwB,CAC/B,KAAO,aAAsB,CAAtB,qBAAwB,CAC/B,KAAO,WAAsB,CAAtB,qBAAwB,CAE/B,MAAQ,oBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,mBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,oBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,kBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,qBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,oBAA6B,CAA7B,4BAA+B,CACvC,MAAQ,kBAA6B,CAA7B,4BAA+B,CAGvC,UAAY,iBAAoB,CAChC,UAAY,iBAAoB,CAChC,OAAS,cAAiB,CAC1B,SAA6B,QAAS,CAAE,MAAO,CAA5B,OAAQ,CAAhB,KAAsC,CAGjD,MAAQ,UAAa,CACrB,MAAQ,UAAa,CACrB,MAAQ,UAAa,CAGrB,KAAO,UAAa,CACpB,KAAO,aAAgB,CACvB,KAAO,YAAe,CACtB,KAAO,UAAa,CACpB,MAAQ,YAAe,CACvB,MAAQ,UAAa,CACrB,QAAU,UAAa,CAEvB,KAAO,WAAc,CACrB,KAAO,cAAiB,CACxB,KAAO,aAAgB,CACvB,KAAO,WAAc,CACrB,MAAQ,aAAgB,CACxB,MAAQ,WAAc,CACtB,QAAU,WAAc,CAGxB,SAAW,qBAA+B,CAA/B,8BAAiC,CAC5C,YAAc,mBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,oBAA+B,CAA/B,8BAAiC,CAC/C,YAAc,kBAA+B,CAA/B,8BAAiC,CAG/C,WAAa,gCAA4B,CAA5B,2BAA8B,CAC3C,WAAa,4DAA4B,CAA5B,2BAA8B,CAC3C,WAAa,8DAA4B,CAA5B,2BAA8B,CAC3C,WAAa,gEAA4B,CAA5B,2BAA8B,CAG3C,gBAAkB,4BAAsC,CAAtC,qCAAwC,CAC1D,mBAAqB,wFAAsH,CAAtH,mHAAwH,CAG7I,WAAa,qFAAgO,CAAhO,wNAAkO,CAG/O,WAAa,SAAY,CACzB,aAAe,SAAY,CAG3B,iBAAmB,eAAkB,CACrC,eAAiB,aAAgB,CAGjC,gBAAkB,cAAiB,CACnC,oBAAsB,kBAAqB,CAG3C,aAAe,wBAAiB,CAAjB,gBAAmB,CAGlC,WAAa,eAAkB,CAC/B,SAAW,gBAAiB,CAAE,iBAAoB,CAj+DlD,yCAi+DmD,CAj+DnD,iBAi+DmD,CAj+DnD,6OAi+DmD,CAj+DnD,wCAi+DmD,CAj+DnD,gBAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,iBAi+DmD,CAj+DnD,yPAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,iBAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,0DAi+DmD,CAj+DnD,uDAi+DmD,CAj+DnD,uDAi+DmD,CAj+DnD,0DAi+DmD,CAj+DnD,2CAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,sDAi+DmD,CAj+DnD,2CAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,2CAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,0CAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,2CAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,qDAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,uDAi+DmD,CAj+DnD,qDAi+DmD,CAj+DnD,iDAi+DmD,CAj+DnD,kGAi+DmD,CAj+DnD,6FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,6FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,8FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,wFAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,+FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,+FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,4FAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,sFAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,0FAi+DmD,CAj+DnD,qDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,sFAi+DmD,CAj+DnD,qDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,yFAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,iEAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,wFAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,oFAi+DmD,CAj+DnD,yGAi+DmD,CAj+DnD,oFAi+DmD,CAj+DnD,yGAi+DmD,CAj+DnD,yFAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,qFAi+DmD,CAj+DnD,yGAi+DmD,CAj+DnD,0FAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,mFAi+DmD,CAj+DnD,yGAi+DmD,CAj+DnD,yFAi+DmD,CAj+DnD,2GAi+DmD,CAj+DnD,sFAi+DmD,CAj+DnD,yGAi+DmD,CAj+DnD,0FAi+DmD,CAj+DnD,oFAi+DmD,CAj+DnD,yFAi+DmD,CAj+DnD,yFAi+DmD,CAj+DnD,mFAi+DmD,CAj+DnD,mFAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,uFAi+DmD,CAj+DnD,sFAi+DmD,CAj+DnD,gFAi+DmD,CAj+DnD,kFAi+DmD,CAj+DnD,oFAi+DmD,CAj+DnD,gFAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,4CAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,4CAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,4CAi+DmD,CAj+DnD,gDAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,8CAi+DmD,CAj+DnD,8CAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,iDAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,6CAi+DmD,CAj+DnD,gEAi+DmD,CAj+DnD,4DAi+DmD,CAj+DnD,gGAi+DmD,CAj+DnD,kGAi+DmD,CAj+DnD,wFAi+DmD,CAj+DnD,kGAi+DmD,CAj+DnD,mDAi+DmD,CAj+DnD,oBAi+DmD,CAj+DnD,uDAi+DmD,CAj+DnD,kDAi+DmD,CAj+DnD,kBAi+DmD,CAj+DnD,+HAi+DmD,CAj+DnD,wGAi+DmD,CAj+DnD,iHAi+DmD,CAj+DnD,wFAi+DmD,CAj+DnD,+HAi+DmD,CAj+DnD,wGAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,0DAi+DmD,CAj+DnD,wDAi+DmD,CAj+DnD,2DAi+DmD,CAj+DnD,yCAi+DmD,CAj+DnD,gBAi+DmD,CAj+DnD,6LAi+DmD,CAj+DnD,yDAi+DmD,CAj+DnD,yCAi+DmD,CAj+DnD,sDAi+DmD,CAj+DnD,uQAi+DmD,CAj+DnD,sDAi+DmD,CAj+DnD,4DAi+DmD,CAj+DnD,aAi+DmD,CAj+DnD,+CAi+DmD,CAj+DnD,gDAi+DmD,CAj+DnD,iDAi+DmD,CAj+DnD,sBAi+DmD,CAj+DnD,wBAi+DmD,CAj+DnD,qBAi+DmD,CAj+DnD,uBAi+DmD,CAj+DnD,oBAi+DmD,CAj+DnD,sBAi+DmD,CAj+DnD,oCAi+DmD,CAj+DnD,gCAi+DmD,CAj+DnD,mEAi+DmD,CAj+DnD,wGAi+DmD,CAj+DnD,mCAi+DmD,CAj+DnD,oCAi+DmD,CAj+DnD,uBAi+DmD,CAj+DnD,qBAi+DmD,CAj+DnD,6BAi+DmD,CAj+DnD,oBAi+DmD,CAj+DnD,6BAi+DmD,CAj+DnD,oBAi+DmD,CAj+DnD,8BAi+DmD,CAj+DnD,gBAi+DmD,CAj+DnD,gCAi+DmD,CAj+DnD,mBAi+DmD,CAj+DnD,+BAi+DmD,CAj+DnD,mBAi+DmD,EAj+DnD,mDAi+DmD,EAj+DnD,oDAi+DmD,CAj+DnD,8DAi+DmD,CAj+DnD,2BAi+DmD,CAj+DnD,kBAi+DmD,EAj+DnD,mEAi+DmD,CAj+DnD,yCAi+DmD,CAj+DnD,8DAi+DmD", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n/* Import Inter font with optimal weights */\n@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');\n\n/* CSS Custom Properties for Design System */\n:root {\n  /* Colors - Notion/Linear inspired */\n  --color-bg-primary: #0a0a0a;\n  --color-bg-secondary: #111111;\n  --color-bg-tertiary: #1a1a1a;\n  --color-bg-elevated: #222222;\n  --color-bg-overlay: rgba(0, 0, 0, 0.8);\n\n  --color-border-primary: rgba(255, 255, 255, 0.08);\n  --color-border-secondary: rgba(255, 255, 255, 0.06);\n  --color-border-focus: rgba(99, 102, 241, 0.4);\n\n  --color-text-primary: #ffffff;\n  --color-text-secondary: rgba(255, 255, 255, 0.7);\n  --color-text-tertiary: rgba(255, 255, 255, 0.5);\n  --color-text-placeholder: rgba(255, 255, 255, 0.3);\n\n  --color-accent-primary: #6366f1;\n  --color-accent-hover: #5855eb;\n  --color-accent-light: rgba(99, 102, 241, 0.1);\n\n  --color-success: #10b981;\n  --color-warning: #f59e0b;\n  --color-error: #ef4444;\n\n  /* Typography Scale */\n  --font-size-xs: 0.75rem;    /* 12px */\n  --font-size-sm: 0.875rem;   /* 14px */\n  --font-size-base: 1rem;     /* 16px */\n  --font-size-lg: 1.125rem;   /* 18px */\n  --font-size-xl: 1.25rem;    /* 20px */\n  --font-size-2xl: 1.5rem;    /* 24px */\n  --font-size-3xl: 1.875rem;  /* 30px */\n\n  /* Line Heights */\n  --line-height-tight: 1.25;\n  --line-height-normal: 1.5;\n  --line-height-relaxed: 1.75;\n\n  /* Spacing Scale */\n  --space-1: 0.25rem;   /* 4px */\n  --space-2: 0.5rem;    /* 8px */\n  --space-3: 0.75rem;   /* 12px */\n  --space-4: 1rem;      /* 16px */\n  --space-5: 1.25rem;   /* 20px */\n  --space-6: 1.5rem;    /* 24px */\n  --space-8: 2rem;      /* 32px */\n  --space-10: 2.5rem;   /* 40px */\n  --space-12: 3rem;     /* 48px */\n  --space-16: 4rem;     /* 64px */\n\n  /* Border Radius */\n  --radius-sm: 0.375rem;  /* 6px */\n  --radius-md: 0.5rem;    /* 8px */\n  --radius-lg: 0.75rem;   /* 12px */\n  --radius-xl: 1rem;      /* 16px */\n\n  /* Shadows */\n  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);\n  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);\n  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);\n  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);\n\n  /* Transitions */\n  --transition-fast: 150ms ease-out;\n  --transition-normal: 250ms ease-out;\n  --transition-slow: 350ms ease-out;\n}\n\n/* Reset and base styles */\n* {\n  box-sizing: border-box;\n  margin: 0;\n  padding: 0;\n}\n\n*::before,\n*::after {\n  box-sizing: border-box;\n}\n\nhtml {\n  font-size: 16px;\n  scroll-behavior: smooth;\n}\n\nbody {\n  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;\n  font-size: var(--font-size-base);\n  line-height: var(--line-height-normal);\n  color: var(--color-text-primary);\n  background: linear-gradient(135deg,\n    var(--color-bg-primary) 0%,\n    var(--color-bg-secondary) 25%,\n    var(--color-bg-tertiary) 75%,\n    var(--color-bg-elevated) 100%\n  );\n  background-attachment: fixed;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  text-rendering: optimizeLegibility;\n  overflow-x: hidden;\n  min-height: 100vh;\n}\n\n/* Typography Classes */\n.text-xs { font-size: var(--font-size-xs); line-height: var(--line-height-tight); }\n.text-sm { font-size: var(--font-size-sm); line-height: var(--line-height-normal); }\n.text-base { font-size: var(--font-size-base); line-height: var(--line-height-normal); }\n.text-lg { font-size: var(--font-size-lg); line-height: var(--line-height-normal); }\n.text-xl { font-size: var(--font-size-xl); line-height: var(--line-height-tight); }\n.text-2xl { font-size: var(--font-size-2xl); line-height: var(--line-height-tight); }\n.text-3xl { font-size: var(--font-size-3xl); line-height: var(--line-height-tight); }\n\n.font-light { font-weight: 300; }\n.font-normal { font-weight: 400; }\n.font-medium { font-weight: 500; }\n.font-semibold { font-weight: 600; }\n.font-bold { font-weight: 700; }\n\n.text-primary { color: var(--color-text-primary); }\n.text-secondary { color: var(--color-text-secondary); }\n.text-tertiary { color: var(--color-text-tertiary); }\n.text-placeholder { color: var(--color-text-placeholder); }\n\n/* Modern Background System */\n.bg-app-gradient {\n  background: linear-gradient(135deg,\n    var(--color-bg-primary) 0%,\n    var(--color-bg-secondary) 25%,\n    var(--color-bg-tertiary) 75%,\n    var(--color-bg-elevated) 100%\n  );\n  background-attachment: fixed;\n  min-height: 100vh;\n  position: relative;\n}\n\n.bg-primary { background-color: var(--color-bg-primary); }\n.bg-secondary { background-color: var(--color-bg-secondary); }\n.bg-tertiary { background-color: var(--color-bg-tertiary); }\n.bg-elevated { background-color: var(--color-bg-elevated); }\n\n/* Modern Card System */\n.card {\n  background: var(--color-bg-elevated);\n  border: 1px solid var(--color-border-primary);\n  border-radius: var(--radius-lg);\n  box-shadow: var(--shadow-md);\n  transition: all var(--transition-normal);\n}\n\n.card:hover {\n  border-color: var(--color-border-secondary);\n  box-shadow: var(--shadow-lg);\n  transform: translateY(-1px);\n}\n\n.card-compact {\n  background: var(--color-bg-secondary);\n  border: 1px solid var(--color-border-secondary);\n  border-radius: var(--radius-md);\n  box-shadow: var(--shadow-sm);\n  transition: all var(--transition-fast);\n}\n\n/* Modern Glassmorphism */\n.glassmorphism {\n  background: rgba(255, 255, 255, 0.03);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1px solid var(--color-border-primary);\n  box-shadow:\n    var(--shadow-lg),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n\n/* Basic utility classes */\n.min-h-screen {\n  min-height: 100vh;\n}\n\n.relative {\n  position: relative;\n}\n\n.fixed {\n  position: fixed;\n}\n\n.absolute {\n  position: absolute;\n}\n\n.inset-0 {\n  top: 0;\n  right: 0;\n  bottom: 0;\n  left: 0;\n}\n\n.overflow-hidden {\n  overflow: hidden;\n}\n\n.pointer-events-none {\n  pointer-events: none;\n}\n\n.z-10 {\n  z-index: 10;\n  position: relative;\n}\n\n.z-50 {\n  z-index: 50;\n  position: relative;\n}\n\n/* Ensure content is above grid */\n.relative.z-10 > * {\n  position: relative;\n  z-index: 2;\n}\n\n/* Flexbox utilities */\n.flex {\n  display: flex;\n}\n\n.inline-flex {\n  display: inline-flex;\n}\n\n.flex-col {\n  flex-direction: column;\n}\n\n.items-center {\n  align-items: center;\n}\n\n.justify-center {\n  justify-content: center;\n}\n\n.justify-between {\n  justify-content: space-between;\n}\n\n.flex-1 {\n  flex: 1 1 0%;\n}\n\n/* Grid utilities */\n.grid {\n  display: grid;\n}\n\n.grid-cols-1 {\n  grid-template-columns: repeat(1, minmax(0, 1fr));\n}\n\n/* Spacing utilities */\n.space-x-2 > * + * {\n  margin-left: 0.5rem;\n}\n\n.space-x-3 > * + * {\n  margin-left: 0.75rem;\n}\n\n.space-x-4 > * + * {\n  margin-left: 1rem;\n}\n\n.space-y-4 > * + * {\n  margin-top: 1rem;\n}\n\n.space-y-6 > * + * {\n  margin-top: 1.5rem;\n}\n\n.gap-4 {\n  gap: 1rem;\n}\n\n.gap-6 {\n  gap: 1.5rem;\n}\n\n/* Padding utilities */\n.p-4 {\n  padding: 1rem;\n}\n\n.p-6 {\n  padding: 1.5rem;\n}\n\n.px-3 {\n  padding-left: 0.75rem;\n  padding-right: 0.75rem;\n}\n\n.px-4 {\n  padding-left: 1rem;\n  padding-right: 1rem;\n}\n\n.px-6 {\n  padding-left: 1.5rem;\n  padding-right: 1.5rem;\n}\n\n.px-8 {\n  padding-left: 2rem;\n  padding-right: 2rem;\n}\n\n.py-2 {\n  padding-top: 0.5rem;\n  padding-bottom: 0.5rem;\n}\n\n.py-3 {\n  padding-top: 0.75rem;\n  padding-bottom: 0.75rem;\n}\n\n.py-4 {\n  padding-top: 1rem;\n  padding-bottom: 1rem;\n}\n\n/* Margin utilities */\n.mb-4 {\n  margin-bottom: 1rem;\n}\n\n.mb-6 {\n  margin-bottom: 1.5rem;\n}\n\n/* Width and height utilities */\n.w-4 {\n  width: 1rem;\n}\n\n.w-6 {\n  width: 1.5rem;\n}\n\n.w-8 {\n  width: 2rem;\n}\n\n.w-10 {\n  width: 2.5rem;\n}\n\n.w-12 {\n  width: 3rem;\n}\n\n.w-full {\n  width: 100%;\n}\n\n.h-4 {\n  height: 1rem;\n}\n\n.h-6 {\n  height: 1.5rem;\n}\n\n.h-8 {\n  height: 2rem;\n}\n\n.h-10 {\n  height: 2.5rem;\n}\n\n.h-12 {\n  height: 3rem;\n}\n\n.h-full {\n  height: 100%;\n}\n\n/* Text utilities */\n.text-xs {\n  font-size: 0.75rem;\n  line-height: 1rem;\n}\n\n.text-sm {\n  font-size: 0.875rem;\n  line-height: 1.25rem;\n}\n\n.text-base {\n  font-size: 1rem;\n  line-height: 1.5rem;\n}\n\n.text-lg {\n  font-size: 1.125rem;\n  line-height: 1.75rem;\n}\n\n.text-xl {\n  font-size: 1.25rem;\n  line-height: 1.75rem;\n}\n\n.text-2xl {\n  font-size: 1.5rem;\n  line-height: 2rem;\n}\n\n.font-medium {\n  font-weight: 500;\n}\n\n.font-semibold {\n  font-weight: 600;\n}\n\n.font-bold {\n  font-weight: 700;\n}\n\n/* Text colors */\n.text-white {\n  color: rgb(255 255 255);\n}\n\n.text-white\\/70 {\n  color: rgb(255 255 255 / 0.7);\n}\n\n.text-white\\/80 {\n  color: rgb(255 255 255 / 0.8);\n}\n\n/* Background colors */\n.bg-white\\/10 {\n  background-color: rgb(255 255 255 / 0.1);\n}\n\n.bg-white\\/20 {\n  background-color: rgb(255 255 255 / 0.2);\n}\n\n/* Border utilities */\n.border {\n  border-width: 1px;\n}\n\n.border-b {\n  border-bottom-width: 1px;\n}\n\n.border-white\\/10 {\n  border-color: rgb(255 255 255 / 0.1);\n}\n\n.border-white\\/20 {\n  border-color: rgb(255 255 255 / 0.2);\n}\n\n.border-white\\/30 {\n  border-color: rgb(255 255 255 / 0.3);\n}\n\n/* Border radius */\n.rounded {\n  border-radius: 0.25rem;\n}\n\n.rounded-lg {\n  border-radius: 0.5rem;\n}\n\n.rounded-xl {\n  border-radius: 0.75rem;\n}\n\n.rounded-2xl {\n  border-radius: 1rem;\n}\n\n.rounded-full {\n  border-radius: 9999px;\n}\n\n/* Shadow utilities */\n.shadow-lg {\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n}\n\n.shadow-xl {\n  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n}\n\n.shadow-md {\n  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);\n}\n\n/* Backdrop filter */\n.backdrop-blur-md {\n  backdrop-filter: blur(12px);\n}\n\n/* Transition utilities */\n.transition-all {\n  transition-property: all;\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n  transition-duration: 150ms;\n}\n\n.duration-300 {\n  transition-duration: 300ms;\n}\n\n.duration-500 {\n  transition-duration: 500ms;\n}\n\n.ease-in-out {\n  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Transform utilities */\n.transform {\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.scale-105 {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.scale-95 {\n  --tw-scale-x: 0.95;\n  --tw-scale-y: 0.95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n/* Hover utilities */\n.hover\\:scale-105:hover {\n  --tw-scale-x: 1.05;\n  --tw-scale-y: 1.05;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n.active\\:scale-95:active {\n  --tw-scale-x: 0.95;\n  --tw-scale-y: 0.95;\n  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));\n}\n\n/* Gradient backgrounds */\n.bg-gradient-to-r {\n  background-image: linear-gradient(to right, var(--tw-gradient-stops));\n}\n\n.bg-gradient-to-tr {\n  background-image: linear-gradient(to top right, var(--tw-gradient-stops));\n}\n\n.bg-gradient-to-bl {\n  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));\n}\n\n.bg-gradient-radial {\n  background-image: radial-gradient(circle, var(--tw-gradient-stops));\n}\n\n/* Gradient color stops */\n.from-purple-900\\/06 {\n  --tw-gradient-from: rgb(88 28 135 / 0.06);\n  --tw-gradient-to: rgb(88 28 135 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.via-transparent {\n  --tw-gradient-to: rgb(255 255 255 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), transparent, var(--tw-gradient-to);\n}\n\n.via-purple-800\\/04 {\n  --tw-gradient-to: rgb(107 33 168 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(107 33 168 / 0.04), var(--tw-gradient-to);\n}\n\n.to-slate-900\\/08 {\n  --tw-gradient-to: rgb(15 23 42 / 0.08);\n}\n\n.from-blue-500 {\n  --tw-gradient-from: #3b82f6;\n  --tw-gradient-to: rgb(59 130 246 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.to-indigo-600 {\n  --tw-gradient-to: #4f46e5;\n}\n\n.from-blue-500\\/80 {\n  --tw-gradient-from: rgb(59 130 246 / 0.8);\n  --tw-gradient-to: rgb(59 130 246 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.via-blue-600\\/70 {\n  --tw-gradient-to: rgb(37 99 235 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(37 99 235 / 0.7), var(--tw-gradient-to);\n}\n\n.to-indigo-600\\/80 {\n  --tw-gradient-to: rgb(79 70 229 / 0.8);\n}\n\n.hover\\:from-blue-500\\/90:hover {\n  --tw-gradient-from: rgb(59 130 246 / 0.9);\n  --tw-gradient-to: rgb(59 130 246 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);\n}\n\n.hover\\:via-blue-600\\/80:hover {\n  --tw-gradient-to: rgb(37 99 235 / 0);\n  --tw-gradient-stops: var(--tw-gradient-from), rgb(37 99 235 / 0.8), var(--tw-gradient-to);\n}\n\n.hover\\:to-indigo-600\\/90:hover {\n  --tw-gradient-to: rgb(79 70 229 / 0.9);\n}\n\n/* Button base styles */\nbutton {\n  cursor: pointer;\n  border: none;\n  outline: none;\n  font-family: inherit;\n}\n\nbutton:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n}\n\n/* Common button classes */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  padding: 0.5rem 1rem;\n  font-size: 0.875rem;\n  font-weight: 500;\n  border-radius: 0.5rem;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  border: 1px solid transparent;\n}\n\n.btn:hover {\n  transform: scale(1.05);\n}\n\n.btn:active {\n  transform: scale(0.95);\n}\n\n/* Primary button */\n.btn-primary {\n  color: white;\n  background: linear-gradient(to right, rgb(59 130 246 / 0.8), rgb(37 99 235 / 0.7), rgb(79 70 229 / 0.8));\n  backdrop-filter: blur(12px);\n  border-color: rgb(59 130 246 / 0.4);\n  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);\n}\n\n.btn-primary:hover {\n  background: linear-gradient(to right, rgb(59 130 246 / 0.9), rgb(37 99 235 / 0.8), rgb(79 70 229 / 0.9));\n  border-color: rgb(59 130 246 / 0.5);\n  box-shadow: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);\n}\n\n/* Grid page backgrounds with animated patterns */\n.grid-page-recording {\n  position: relative;\n}\n\n.grid-page-recording::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 120px 120px, rgba(59, 130, 246, 0.4) 2px, transparent 2px),\n    radial-gradient(circle at 60px 60px, rgba(147, 51, 234, 0.3) 1px, transparent 1px);\n  background-size: 120px 120px, 60px 60px;\n  background-position: 0 0, 30px 30px;\n  animation: grid-float 20s ease-in-out infinite;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.grid-page-transcription {\n  position: relative;\n}\n\n.grid-page-transcription::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 120px 120px, rgba(168, 85, 247, 0.4) 2px, transparent 2px),\n    radial-gradient(circle at 60px 60px, rgba(236, 72, 153, 0.3) 1px, transparent 1px);\n  background-size: 120px 120px, 60px 60px;\n  background-position: 0 0, 30px 30px;\n  animation: grid-pulse 15s ease-in-out infinite;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.grid-page-results {\n  position: relative;\n}\n\n.grid-page-results::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 120px 120px, rgba(34, 197, 94, 0.4) 2px, transparent 2px),\n    radial-gradient(circle at 60px 60px, rgba(16, 185, 129, 0.3) 1px, transparent 1px);\n  background-size: 120px 120px, 60px 60px;\n  background-position: 0 0, 30px 30px;\n  animation: grid-wave 25s ease-in-out infinite;\n  pointer-events: none;\n  z-index: 1;\n}\n\n.grid-page-home {\n  position: relative;\n}\n\n.grid-page-home::before {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-image:\n    radial-gradient(circle at 120px 120px, rgba(147, 51, 234, 0.3) 2px, transparent 2px),\n    radial-gradient(circle at 60px 60px, rgba(79, 70, 229, 0.2) 1px, transparent 1px);\n  background-size: 120px 120px, 60px 60px;\n  background-position: 0 0, 30px 30px;\n  pointer-events: none;\n  z-index: 1;\n}\n\n/* Grid animations */\n@keyframes grid-float {\n  0%, 100% {\n    background-position: 0 0;\n    opacity: 0.7;\n  }\n  50% {\n    background-position: 20px 20px;\n    opacity: 1;\n  }\n}\n\n@keyframes grid-pulse {\n  0%, 100% {\n    background-position: 0 0;\n    opacity: 0.6;\n  }\n  25% {\n    background-position: 10px 0;\n    opacity: 0.9;\n  }\n  75% {\n    background-position: -10px 10px;\n    opacity: 0.8;\n  }\n}\n\n@keyframes grid-wave {\n  0%, 100% {\n    background-position: 0 0;\n    opacity: 0.5;\n  }\n  33% {\n    background-position: 15px 5px;\n    opacity: 0.8;\n  }\n  66% {\n    background-position: -5px 15px;\n    opacity: 0.9;\n  }\n}\n\n/* Custom animations */\n@keyframes fade-in {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n\n@keyframes fade-in-up {\n  from {\n    opacity: 0;\n    transform: translateY(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes fade-in-down {\n  from {\n    opacity: 0;\n    transform: translateY(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateY(0);\n  }\n}\n\n@keyframes slide-in-left {\n  from {\n    opacity: 0;\n    transform: translateX(-20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes slide-in-right {\n  from {\n    opacity: 0;\n    transform: translateX(20px);\n  }\n  to {\n    opacity: 1;\n    transform: translateX(0);\n  }\n}\n\n@keyframes scale-in {\n  from {\n    opacity: 0;\n    transform: scale(0.9);\n  }\n  to {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n@keyframes bounce-in {\n  0% {\n    opacity: 0;\n    transform: scale(0.3);\n  }\n  50% {\n    opacity: 1;\n    transform: scale(1.05);\n  }\n  70% {\n    transform: scale(0.9);\n  }\n  100% {\n    opacity: 1;\n    transform: scale(1);\n  }\n}\n\n/* Animation classes */\n.animate-fade-in {\n  animation: fade-in 0.6s ease-out;\n}\n\n.animate-fade-in-up {\n  animation: fade-in-up 0.6s ease-out;\n}\n\n.animate-fade-in-down {\n  animation: fade-in-down 0.6s ease-out;\n}\n\n.animate-slide-in-left {\n  animation: slide-in-left 0.6s ease-out;\n}\n\n.animate-slide-in-right {\n  animation: slide-in-right 0.6s ease-out;\n}\n\n.animate-scale-in {\n  animation: scale-in 0.6s ease-out;\n}\n\n.animate-bounce-in {\n  animation: bounce-in 0.8s ease-out;\n}\n\n/* Animation delays */\n.animation-delay-100 {\n  animation-delay: 100ms;\n}\n\n.animation-delay-200 {\n  animation-delay: 200ms;\n}\n\n.animation-delay-300 {\n  animation-delay: 300ms;\n}\n\n.animation-delay-400 {\n  animation-delay: 400ms;\n}\n\n.animation-delay-500 {\n  animation-delay: 500ms;\n}\n\n/* Smooth transitions */\n.transition-ultra {\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.transition-smooth {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Glassmorphism effects */\n.glassmorphism {\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(25px);\n  -webkit-backdrop-filter: blur(25px);\n  border: 1px solid rgba(255, 255, 255, 0.25);\n  box-shadow:\n    0 8px 32px rgba(0, 0, 0, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3);\n}\n\n/* Enhanced glassmorphism for headers */\n.glassmorphism-header {\n  background: rgba(255, 255, 255, 0.08);\n  backdrop-filter: blur(25px);\n  -webkit-backdrop-filter: blur(25px);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  box-shadow:\n    0 2px 15px rgba(0, 0, 0, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.15);\n}\n\n/* Elegant Navbar Styles */\n.elegant-navbar-glass {\n  background: rgba(0, 0, 0, 0.4);\n  backdrop-filter: blur(40px);\n  -webkit-backdrop-filter: blur(40px);\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n  box-shadow:\n    0 4px 32px rgba(0, 0, 0, 0.3),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Elegant Logo Container */\n.elegant-logo-container {\n  width: 40px;\n  height: 40px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);\n  border-radius: 12px;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  box-shadow:\n    0 8px 25px rgba(59, 130, 246, 0.3),\n    0 4px 12px rgba(139, 92, 246, 0.2);\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n}\n\n.elegant-logo-container::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.elegant-logo-container:hover::before {\n  opacity: 1;\n}\n\n.elegant-logo-container:hover {\n  transform: translateY(-2px) scale(1.05);\n  box-shadow:\n    0 12px 35px rgba(59, 130, 246, 0.4),\n    0 6px 16px rgba(139, 92, 246, 0.3);\n}\n\n/* Elegant Navigation Container */\n.elegant-nav-container {\n  position: relative;\n}\n\n.elegant-nav-pills {\n  display: flex;\n  align-items: center;\n  gap: 4px;\n  padding: 6px;\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  border-radius: 16px;\n  box-shadow:\n    0 8px 32px rgba(0, 0, 0, 0.2),\n    inset 0 1px 0 rgba(255, 255, 255, 0.1);\n}\n\n/* Elegant Navigation Pills */\n.elegant-nav-pill {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 16px;\n  border-radius: 12px;\n  font-size: 14px;\n  font-weight: 500;\n  color: rgba(255, 255, 255, 0.7);\n  background: transparent;\n  border: none;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.elegant-nav-pill::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.elegant-nav-pill:hover::before {\n  opacity: 1;\n}\n\n.elegant-nav-pill:hover {\n  color: rgba(255, 255, 255, 0.9);\n  transform: translateY(-1px);\n}\n\n.elegant-nav-pill.active {\n  color: white;\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.8) 0%, rgba(139, 92, 246, 0.6) 100%);\n  box-shadow:\n    0 4px 16px rgba(59, 130, 246, 0.3),\n    0 2px 8px rgba(139, 92, 246, 0.2);\n  transform: translateY(-1px);\n}\n\n.elegant-nav-pill.active::before {\n  opacity: 0;\n}\n\n/* Elegant Status Container */\n.elegant-status-container {\n  position: relative;\n}\n\n/* Elegant Whisper Status */\n.elegant-whisper-status {\n  display: inline-flex;\n  align-items: center;\n  gap: 8px;\n  padding: 8px 14px;\n  background: rgba(16, 185, 129, 0.1);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1px solid rgba(16, 185, 129, 0.2);\n  border-radius: 12px;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.elegant-whisper-status::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: linear-gradient(135deg, rgba(16, 185, 129, 0.05) 0%, rgba(5, 150, 105, 0.05) 100%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.elegant-whisper-status:hover::before {\n  opacity: 1;\n}\n\n/* Elegant Status Dot */\n.elegant-status-dot {\n  width: 8px;\n  height: 8px;\n  border-radius: 50%;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n}\n\n.elegant-status-dot.connected {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  box-shadow:\n    0 0 12px rgba(16, 185, 129, 0.6),\n    0 0 24px rgba(16, 185, 129, 0.3);\n  animation: pulse-glow 2s infinite;\n}\n\n.elegant-status-dot.disconnected {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  box-shadow:\n    0 0 12px rgba(239, 68, 68, 0.6),\n    0 0 24px rgba(239, 68, 68, 0.3);\n  animation: pulse-glow 2s infinite;\n}\n\n@keyframes pulse-glow {\n  0%, 100% {\n    transform: scale(1);\n    opacity: 1;\n  }\n  50% {\n    transform: scale(1.2);\n    opacity: 0.8;\n  }\n}\n\n/* Elegant Demo Button */\n.elegant-demo-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 10px 16px;\n  background: rgba(245, 158, 11, 0.1);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  border: 1px solid rgba(245, 158, 11, 0.3);\n  border-radius: 12px;\n  color: rgb(245, 158, 11);\n  font-size: 14px;\n  font-weight: 500;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n}\n\n.elegant-demo-button::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: linear-gradient(135deg, rgba(245, 158, 11, 0.1) 0%, rgba(251, 191, 36, 0.1) 100%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.elegant-demo-button:hover::before {\n  opacity: 1;\n}\n\n.elegant-demo-button:hover {\n  background: rgba(245, 158, 11, 0.15);\n  border-color: rgba(245, 158, 11, 0.4);\n  color: rgb(251, 191, 36);\n  transform: translateY(-2px);\n  box-shadow:\n    0 8px 25px rgba(245, 158, 11, 0.2),\n    0 4px 12px rgba(245, 158, 11, 0.1);\n}\n\n/* Elegant Primary Button */\n.elegant-primary-button {\n  display: flex;\n  align-items: center;\n  gap: 8px;\n  padding: 12px 20px;\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 50%, #06b6d4 100%);\n  border: none;\n  border-radius: 12px;\n  color: white;\n  font-size: 14px;\n  font-weight: 600;\n  cursor: pointer;\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n  position: relative;\n  overflow: hidden;\n  box-shadow:\n    0 8px 25px rgba(59, 130, 246, 0.3),\n    0 4px 12px rgba(139, 92, 246, 0.2);\n}\n\n.elegant-primary-button::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%);\n  border-radius: inherit;\n  opacity: 0;\n  transition: opacity 0.3s ease;\n}\n\n.elegant-primary-button:hover::before {\n  opacity: 1;\n}\n\n.elegant-primary-button:hover {\n  transform: translateY(-3px) scale(1.02);\n  box-shadow:\n    0 12px 35px rgba(59, 130, 246, 0.4),\n    0 6px 16px rgba(139, 92, 246, 0.3);\n}\n\n/* Responsive Design for Elegant Navbar */\n@media (max-width: 768px) {\n  .elegant-nav-pills {\n    gap: 2px;\n    padding: 4px;\n  }\n\n  .elegant-nav-pill {\n    padding: 8px 12px;\n    font-size: 13px;\n  }\n\n  .elegant-logo-container {\n    width: 36px;\n    height: 36px;\n  }\n\n  .elegant-primary-button {\n    padding: 10px 16px;\n    font-size: 13px;\n  }\n\n  .elegant-demo-button {\n    padding: 8px 12px;\n    font-size: 13px;\n  }\n\n  .elegant-whisper-status {\n    padding: 6px 10px;\n    font-size: 13px;\n  }\n}\n\n@media (max-width: 640px) {\n  .elegant-navbar-glass {\n    backdrop-filter: blur(30px);\n    -webkit-backdrop-filter: blur(30px);\n  }\n\n  .elegant-nav-pills {\n    padding: 3px;\n  }\n\n  .elegant-nav-pill {\n    padding: 6px 8px;\n    font-size: 12px;\n  }\n\n  .elegant-logo-container {\n    width: 32px;\n    height: 32px;\n  }\n}\n\n/* Elegant Navbar Entrance Animation */\n@keyframes navbar-slide-down {\n  from {\n    transform: translateY(-100%);\n    opacity: 0;\n  }\n  to {\n    transform: translateY(0);\n    opacity: 1;\n  }\n}\n\n.elegant-navbar-glass {\n  animation: navbar-slide-down 0.6s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n/* Subtle hover effects for better UX */\n.elegant-nav-container:hover .elegant-nav-pills {\n  box-shadow:\n    0 12px 40px rgba(0, 0, 0, 0.25),\n    inset 0 1px 0 rgba(255, 255, 255, 0.15);\n}\n\n/* Enhanced focus states for accessibility */\n.elegant-nav-pill:focus-visible,\n.elegant-demo-button:focus-visible,\n.elegant-primary-button:focus-visible {\n  outline: 2px solid rgba(59, 130, 246, 0.6);\n  outline-offset: 2px;\n}\n\n/* Logo animation */\n.logo-icon {\n  transition: all 0.2s ease-out;\n}\n\n.logo-icon:hover {\n  transform: scale(1.05) rotate(5deg);\n}\n\n/* Gradient backgrounds */\n.bg-unique-gradient-1 {\n  background: linear-gradient(135deg, rgba(147, 51, 234, 0.1) 0%, rgba(79, 70, 229, 0.1) 100%);\n}\n\n.bg-unique-gradient-2 {\n  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(147, 51, 234, 0.1) 100%);\n}\n\n.bg-unique-gradient-3 {\n  background: linear-gradient(135deg, rgba(16, 185, 129, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);\n}\n\n/* Shadow effects */\n.shadow-elegant {\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1), 0 4px 10px rgba(0, 0, 0, 0.05);\n}\n\n.shadow-primary {\n  box-shadow: 0 20px 40px rgba(147, 51, 234, 0.15), 0 8px 16px rgba(147, 51, 234, 0.1);\n}\n\n.shadow-soft {\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);\n}\n\n/* Hover effects */\n.hover-gradient-shift {\n  transition: background 0.3s ease;\n}\n\n.hover-gradient-shift:hover {\n  background: linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(79, 70, 229, 0.15) 100%);\n}\n\n.float-effect {\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\n}\n\n.float-effect:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);\n}\n\n/* Pulse effects */\n.pulse-subtle {\n  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n}\n\n/* Gradient borders */\n.gradient-border-fade {\n  position: relative;\n  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);\n  border-radius: 1rem;\n}\n\n.gradient-border-fade::before {\n  content: '';\n  position: absolute;\n  inset: 0;\n  padding: 1px;\n  background: linear-gradient(135deg, rgba(147, 51, 234, 0.3) 0%, rgba(79, 70, 229, 0.3) 100%);\n  border-radius: inherit;\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: exclude;\n}\n\n/* Button styles */\n.btn-unique {\n  @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-300;\n}\n\n.btn-unique-primary {\n  @apply text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50;\n}\n\n.btn-unique-icon {\n  @apply space-x-2;\n}\n\n.btn-unique-lg {\n  @apply px-6 py-3 text-base font-semibold;\n}\n\n/* Responsive text */\n@media (max-width: 640px) {\n  .text-responsive {\n    font-size: 0.875rem;\n  }\n}\n\n@media (min-width: 641px) {\n  .text-responsive {\n    font-size: 1rem;\n  }\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: rgba(255, 255, 255, 0.1);\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb {\n  background: rgba(255, 255, 255, 0.3);\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: rgba(255, 255, 255, 0.5);\n}\n\n/* Focus styles */\n.focus-ring {\n  @apply focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:ring-offset-2 focus:ring-offset-transparent;\n}\n\n/* Loading states */\n.loading-shimmer {\n  background: linear-gradient(90deg, rgba(255, 255, 255, 0.1) 25%, rgba(255, 255, 255, 0.2) 50%, rgba(255, 255, 255, 0.1) 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n\n/* Additional gradient utilities */\n.bg-gradient-radial {\n  background: radial-gradient(circle, var(--tw-gradient-stops));\n}\n\n/* Enhanced glassmorphism variants */\n.glassmorphism-strong {\n  background: rgba(255, 255, 255, 0.15);\n  backdrop-filter: blur(25px);\n  border: 1px solid rgba(255, 255, 255, 0.25);\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\n}\n\n.glassmorphism-subtle {\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(15px);\n  border: 1px solid rgba(255, 255, 255, 0.1);\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.05);\n}\n\n/* Button hover effects */\n.btn-hover-lift {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.btn-hover-lift:hover {\n  transform: translateY(-2px);\n  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);\n}\n\n/* Text gradients */\n.text-gradient-primary {\n  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n/* Status indicators */\n.status-recording {\n  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);\n  box-shadow: 0 0 20px rgba(239, 68, 68, 0.4);\n  animation: pulse 2s infinite;\n}\n\n.status-processing {\n  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);\n  box-shadow: 0 0 20px rgba(245, 158, 11, 0.4);\n  animation: pulse 2s infinite;\n}\n\n.status-complete {\n  background: linear-gradient(135deg, #10b981 0%, #059669 100%);\n  box-shadow: 0 0 20px rgba(16, 185, 129, 0.4);\n}\n\n/* Modern Responsive System */\n/* Mobile First Approach */\n\n/* Extra Small devices (phones, 475px and down) */\n@media (max-width: 475px) {\n  :root {\n    --font-size-base: 0.875rem;\n    --space-4: 0.75rem;\n    --space-6: 1rem;\n  }\n\n  .xs\\:hidden { display: none !important; }\n  .xs\\:block { display: block !important; }\n  .xs\\:flex { display: flex !important; }\n  .xs\\:grid { display: grid !important; }\n  .xs\\:text-xs { font-size: var(--font-size-xs) !important; }\n  .xs\\:text-sm { font-size: var(--font-size-sm) !important; }\n  .xs\\:p-2 { padding: var(--space-2) !important; }\n  .xs\\:p-3 { padding: var(--space-3) !important; }\n  .xs\\:gap-2 { gap: var(--space-2) !important; }\n  .xs\\:gap-3 { gap: var(--space-3) !important; }\n}\n\n/* Small devices (tablets, 640px and up) */\n@media (min-width: 640px) {\n  .sm\\:hidden { display: none !important; }\n  .sm\\:block { display: block !important; }\n  .sm\\:flex { display: flex !important; }\n  .sm\\:grid { display: grid !important; }\n  .sm\\:text-sm { font-size: var(--font-size-sm) !important; }\n  .sm\\:text-base { font-size: var(--font-size-base) !important; }\n  .sm\\:text-lg { font-size: var(--font-size-lg) !important; }\n  .sm\\:p-4 { padding: var(--space-4) !important; }\n  .sm\\:p-6 { padding: var(--space-6) !important; }\n  .sm\\:gap-4 { gap: var(--space-4) !important; }\n  .sm\\:gap-6 { gap: var(--space-6) !important; }\n}\n\n/* Medium devices (laptops, 768px and up) */\n@media (min-width: 768px) {\n  .md\\:hidden { display: none !important; }\n  .md\\:block { display: block !important; }\n  .md\\:flex { display: flex !important; }\n  .md\\:grid { display: grid !important; }\n  .md\\:text-base { font-size: var(--font-size-base) !important; }\n  .md\\:text-lg { font-size: var(--font-size-lg) !important; }\n  .md\\:text-xl { font-size: var(--font-size-xl) !important; }\n  .md\\:p-6 { padding: var(--space-6) !important; }\n  .md\\:p-8 { padding: var(--space-8) !important; }\n  .md\\:gap-6 { gap: var(--space-6) !important; }\n  .md\\:gap-8 { gap: var(--space-8) !important; }\n}\n\n/* Large devices (desktops, 1024px and up) */\n@media (min-width: 1024px) {\n  .lg\\:hidden { display: none !important; }\n  .lg\\:block { display: block !important; }\n  .lg\\:flex { display: flex !important; }\n  .lg\\:grid { display: grid !important; }\n  .lg\\:text-lg { font-size: var(--font-size-lg) !important; }\n  .lg\\:text-xl { font-size: var(--font-size-xl) !important; }\n  .lg\\:text-2xl { font-size: var(--font-size-2xl) !important; }\n  .lg\\:p-8 { padding: var(--space-8) !important; }\n  .lg\\:p-12 { padding: var(--space-12) !important; }\n  .lg\\:gap-8 { gap: var(--space-8) !important; }\n  .lg\\:gap-12 { gap: var(--space-12) !important; }\n}\n\n/* Extra Large devices (large desktops, 1280px and up) */\n@media (min-width: 1280px) {\n  .xl\\:hidden { display: none !important; }\n  .xl\\:block { display: block !important; }\n  .xl\\:flex { display: flex !important; }\n  .xl\\:grid { display: grid !important; }\n  .xl\\:text-xl { font-size: var(--font-size-xl) !important; }\n  .xl\\:text-2xl { font-size: var(--font-size-2xl) !important; }\n  .xl\\:text-3xl { font-size: var(--font-size-3xl) !important; }\n  .xl\\:p-12 { padding: var(--space-12) !important; }\n  .xl\\:p-16 { padding: var(--space-16) !important; }\n  .xl\\:gap-12 { gap: var(--space-12) !important; }\n  .xl\\:gap-16 { gap: var(--space-16) !important; }\n}\n\n/* Modern Button System */\n.btn {\n  display: inline-flex;\n  align-items: center;\n  justify-content: center;\n  gap: var(--space-2);\n  padding: var(--space-2) var(--space-4);\n  font-size: var(--font-size-sm);\n  font-weight: 500;\n  line-height: 1;\n  border: none;\n  border-radius: var(--radius-md);\n  cursor: pointer;\n  transition: all var(--transition-fast);\n  text-decoration: none;\n  white-space: nowrap;\n  user-select: none;\n  outline: none;\n}\n\n.btn:focus-visible {\n  outline: 2px solid var(--color-accent-primary);\n  outline-offset: 2px;\n}\n\n.btn:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  pointer-events: none;\n}\n\n/* Button Variants */\n.btn-primary {\n  background: var(--color-accent-primary);\n  color: white;\n  box-shadow: var(--shadow-sm);\n}\n\n.btn-primary:hover:not(:disabled) {\n  background: var(--color-accent-hover);\n  box-shadow: var(--shadow-md);\n  transform: translateY(-1px);\n}\n\n.btn-secondary {\n  background: var(--color-bg-elevated);\n  color: var(--color-text-primary);\n  border: 1px solid var(--color-border-primary);\n}\n\n.btn-secondary:hover:not(:disabled) {\n  background: var(--color-bg-tertiary);\n  border-color: var(--color-border-secondary);\n  transform: translateY(-1px);\n}\n\n.btn-ghost {\n  background: transparent;\n  color: var(--color-text-secondary);\n}\n\n.btn-ghost:hover:not(:disabled) {\n  background: var(--color-bg-elevated);\n  color: var(--color-text-primary);\n}\n\n/* Button Sizes */\n.btn-sm {\n  padding: var(--space-1) var(--space-3);\n  font-size: var(--font-size-xs);\n  gap: var(--space-1);\n}\n\n.btn-lg {\n  padding: var(--space-3) var(--space-6);\n  font-size: var(--font-size-base);\n  gap: var(--space-3);\n}\n\n/* Icon Button */\n.btn-icon {\n  padding: var(--space-2);\n  width: 2.5rem;\n  height: 2.5rem;\n}\n\n.btn-icon.btn-sm {\n  padding: var(--space-1);\n  width: 2rem;\n  height: 2rem;\n}\n\n/* Card styles */\n.card {\n  @apply bg-white/10 backdrop-blur-md border border-white/20 rounded-xl shadow-lg;\n}\n\n.card-header {\n  @apply px-6 py-4 border-b border-white/10;\n}\n\n.card-body {\n  @apply px-6 py-4;\n}\n\n.card-footer {\n  @apply px-6 py-4 border-t border-white/10;\n}\n\n/* Input styles */\n.input-primary {\n  @apply w-full px-4 py-2 text-white bg-white/10 border border-white/20 rounded-lg backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-blue-500/50 focus:border-blue-500/50 placeholder-white/50;\n}\n\n/* Loading spinner */\n.spinner {\n  @apply inline-block w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin;\n}\n\n/* Modern Tooltip System */\n.tooltip {\n  position: relative;\n  display: inline-block;\n}\n\n.tooltip::before {\n  content: attr(data-tooltip);\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%) translateY(-8px);\n  background: var(--color-bg-overlay);\n  color: var(--color-text-primary);\n  padding: var(--space-2) var(--space-3);\n  border-radius: var(--radius-md);\n  font-size: var(--font-size-xs);\n  font-weight: 500;\n  white-space: nowrap;\n  opacity: 0;\n  visibility: hidden;\n  transition: all var(--transition-fast);\n  z-index: 1000;\n  backdrop-filter: blur(20px);\n  border: 1px solid var(--color-border-primary);\n  box-shadow: var(--shadow-lg);\n}\n\n.tooltip::after {\n  content: '';\n  position: absolute;\n  bottom: 100%;\n  left: 50%;\n  transform: translateX(-50%) translateY(-2px);\n  border: 4px solid transparent;\n  border-top-color: var(--color-bg-overlay);\n  opacity: 0;\n  visibility: hidden;\n  transition: all var(--transition-fast);\n  z-index: 1000;\n}\n\n.tooltip:hover::before,\n.tooltip:hover::after {\n  opacity: 1;\n  visibility: visible;\n  transform: translateX(-50%) translateY(-4px);\n}\n\n/* Modern Input System */\n.input {\n  width: 100%;\n  padding: var(--space-3) var(--space-4);\n  background: var(--color-bg-secondary);\n  border: 1px solid var(--color-border-primary);\n  border-radius: var(--radius-md);\n  color: var(--color-text-primary);\n  font-size: var(--font-size-sm);\n  line-height: var(--line-height-normal);\n  transition: all var(--transition-fast);\n  outline: none;\n}\n\n.input::placeholder {\n  color: var(--color-text-placeholder);\n}\n\n.input:focus {\n  border-color: var(--color-accent-primary);\n  box-shadow: 0 0 0 3px var(--color-accent-light);\n  background: var(--color-bg-tertiary);\n}\n\n.input:disabled {\n  opacity: 0.5;\n  cursor: not-allowed;\n  background: var(--color-bg-primary);\n}\n\n/* Input Sizes */\n.input-sm {\n  padding: var(--space-2) var(--space-3);\n  font-size: var(--font-size-xs);\n}\n\n.input-lg {\n  padding: var(--space-4) var(--space-5);\n  font-size: var(--font-size-base);\n}\n\n/* Modern Progress System */\n.progress {\n  width: 100%;\n  height: 6px;\n  background: var(--color-bg-tertiary);\n  border-radius: var(--radius-sm);\n  overflow: hidden;\n  position: relative;\n}\n\n.progress-fill {\n  height: 100%;\n  background: linear-gradient(90deg, var(--color-accent-primary), var(--color-accent-hover));\n  border-radius: var(--radius-sm);\n  transition: width var(--transition-normal);\n  position: relative;\n}\n\n.progress-fill::after {\n  content: '';\n  position: absolute;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);\n  animation: shimmer 2s infinite;\n}\n\n@keyframes shimmer {\n  0% { transform: translateX(-100%); }\n  100% { transform: translateX(100%); }\n}\n\n/* Layout Utilities */\n.flex { display: flex; }\n.inline-flex { display: inline-flex; }\n.grid { display: grid; }\n.hidden { display: none; }\n.block { display: block; }\n\n.flex-col { flex-direction: column; }\n.flex-row { flex-direction: row; }\n.items-center { align-items: center; }\n.items-start { align-items: flex-start; }\n.items-end { align-items: flex-end; }\n.justify-center { justify-content: center; }\n.justify-between { justify-content: space-between; }\n.justify-start { justify-content: flex-start; }\n.justify-end { justify-content: flex-end; }\n\n.flex-1 { flex: 1 1 0%; }\n.flex-none { flex: none; }\n\n/* Spacing Utilities */\n.gap-1 { gap: var(--space-1); }\n.gap-2 { gap: var(--space-2); }\n.gap-3 { gap: var(--space-3); }\n.gap-4 { gap: var(--space-4); }\n.gap-5 { gap: var(--space-5); }\n.gap-6 { gap: var(--space-6); }\n.gap-8 { gap: var(--space-8); }\n\n.p-1 { padding: var(--space-1); }\n.p-2 { padding: var(--space-2); }\n.p-3 { padding: var(--space-3); }\n.p-4 { padding: var(--space-4); }\n.p-5 { padding: var(--space-5); }\n.p-6 { padding: var(--space-6); }\n.p-8 { padding: var(--space-8); }\n\n.px-1 { padding-left: var(--space-1); padding-right: var(--space-1); }\n.px-2 { padding-left: var(--space-2); padding-right: var(--space-2); }\n.px-3 { padding-left: var(--space-3); padding-right: var(--space-3); }\n.px-4 { padding-left: var(--space-4); padding-right: var(--space-4); }\n.px-5 { padding-left: var(--space-5); padding-right: var(--space-5); }\n.px-6 { padding-left: var(--space-6); padding-right: var(--space-6); }\n.px-8 { padding-left: var(--space-8); padding-right: var(--space-8); }\n\n.py-1 { padding-top: var(--space-1); padding-bottom: var(--space-1); }\n.py-2 { padding-top: var(--space-2); padding-bottom: var(--space-2); }\n.py-3 { padding-top: var(--space-3); padding-bottom: var(--space-3); }\n.py-4 { padding-top: var(--space-4); padding-bottom: var(--space-4); }\n.py-5 { padding-top: var(--space-5); padding-bottom: var(--space-5); }\n.py-6 { padding-top: var(--space-6); padding-bottom: var(--space-6); }\n.py-8 { padding-top: var(--space-8); padding-bottom: var(--space-8); }\n\n.m-1 { margin: var(--space-1); }\n.m-2 { margin: var(--space-2); }\n.m-3 { margin: var(--space-3); }\n.m-4 { margin: var(--space-4); }\n.m-5 { margin: var(--space-5); }\n.m-6 { margin: var(--space-6); }\n.m-8 { margin: var(--space-8); }\n\n.mb-1 { margin-bottom: var(--space-1); }\n.mb-2 { margin-bottom: var(--space-2); }\n.mb-3 { margin-bottom: var(--space-3); }\n.mb-4 { margin-bottom: var(--space-4); }\n.mb-5 { margin-bottom: var(--space-5); }\n.mb-6 { margin-bottom: var(--space-6); }\n.mb-8 { margin-bottom: var(--space-8); }\n\n/* Positioning */\n.relative { position: relative; }\n.absolute { position: absolute; }\n.fixed { position: fixed; }\n.inset-0 { top: 0; right: 0; bottom: 0; left: 0; }\n\n/* Z-index */\n.z-10 { z-index: 10; }\n.z-20 { z-index: 20; }\n.z-50 { z-index: 50; }\n\n/* Width & Height */\n.w-4 { width: 1rem; }\n.w-5 { width: 1.25rem; }\n.w-6 { width: 1.5rem; }\n.w-8 { width: 2rem; }\n.w-10 { width: 2.5rem; }\n.w-12 { width: 3rem; }\n.w-full { width: 100%; }\n\n.h-4 { height: 1rem; }\n.h-5 { height: 1.25rem; }\n.h-6 { height: 1.5rem; }\n.h-8 { height: 2rem; }\n.h-10 { height: 2.5rem; }\n.h-12 { height: 3rem; }\n.h-full { height: 100%; }\n\n/* Border Radius */\n.rounded { border-radius: var(--radius-sm); }\n.rounded-md { border-radius: var(--radius-md); }\n.rounded-lg { border-radius: var(--radius-lg); }\n.rounded-xl { border-radius: var(--radius-xl); }\n\n/* Shadows */\n.shadow-sm { box-shadow: var(--shadow-sm); }\n.shadow-md { box-shadow: var(--shadow-md); }\n.shadow-lg { box-shadow: var(--shadow-lg); }\n.shadow-xl { box-shadow: var(--shadow-xl); }\n\n/* Transitions */\n.transition-all { transition: all var(--transition-fast); }\n.transition-colors { transition: color var(--transition-fast), background-color var(--transition-fast), border-color var(--transition-fast); }\n\n/* Transforms */\n.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }\n\n/* Opacity */\n.opacity-0 { opacity: 0; }\n.opacity-100 { opacity: 1; }\n\n/* Overflow */\n.overflow-hidden { overflow: hidden; }\n.overflow-auto { overflow: auto; }\n\n/* Cursor */\n.cursor-pointer { cursor: pointer; }\n.cursor-not-allowed { cursor: not-allowed; }\n\n/* User Select */\n.select-none { user-select: none; }\n\n/* Max Width */\n.max-w-7xl { max-width: 80rem; }\n.mx-auto { margin-left: auto; margin-right: auto; }"], "names": [], "sourceRoot": ""}