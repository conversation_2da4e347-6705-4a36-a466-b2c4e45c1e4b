{"ast": null, "code": "import React,{useState}from'react';import{<PERSON><PERSON><PERSON>,<PERSON>rid,RotateCw}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const GridControls=_ref=>{let{onGridSizeChange,onGridRotationChange,onGridColorChange,currentSize,currentRotation,currentColor}=_ref;const[isOpen,setIsOpen]=useState(false);const sizeOptions=[40,60,80,100,120,140,160,180,200,220,240];const rotationOptions=[0,15,30,45,60,75,90,105,120,135,150,165,180];const colorOptions=[{name:'<PERSON><PERSON><PERSON>',value:'rgba(196, 181, 253, 0.15)'},{name:'<PERSON><PERSON><PERSON><PERSON>',value:'rgba(147, 51, 234, 0.25)'},{name:'<PERSON><PERSON><PERSON><PERSON>',value:'rgba(147, 51, 234, 0.40)'},{name:'<PERSON><PERSON>',value:'rgba(88, 28, 135, 0.35)'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"fixed bottom-4 right-4 z-50\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>setIsOpen(!isOpen),className:\"bg-gradient-to-r from-white/15 via-white/10 to-white/15 backdrop-blur-xl border border-white/20 rounded-lg sm:rounded-xl p-2 sm:p-3 shadow-2xl hover:shadow-3xl transition-all duration-300\",title:\"Tinklelio nustatymai\",children:/*#__PURE__*/_jsx(Settings,{className:\"h-4 w-4 sm:h-5 sm:w-5 text-white/80\"})}),isOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"absolute bottom-16 right-0 bg-gradient-to-br from-white/10 via-white/8 to-white/10 backdrop-blur-2xl border border-white/20 rounded-xl sm:rounded-2xl shadow-2xl p-3 sm:p-4 min-w-[240px] sm:min-w-[280px]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 sm:space-y-4\",children:[/*#__PURE__*/_jsxs(\"h3\",{className:\"text-sm font-semibold text-white/90 flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(Grid,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Tinklelio nustatymai\"})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"block text-xs font-medium text-white/70 mb-2\",children:[\"Tinklelio dydis: \",currentSize,\"px\"]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-4 gap-1\",children:sizeOptions.map(size=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>onGridSizeChange(size),className:`px-2 py-1 text-xs rounded-md transition-all duration-300 ${currentSize===size?'bg-gradient-to-r from-blue-500/60 to-indigo-500/60 text-white shadow-lg backdrop-blur-md border border-blue-400/40':'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'}`,children:size},size))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsxs(\"label\",{className:\"block text-xs font-medium text-white/70 mb-2 flex items-center space-x-1\",children:[/*#__PURE__*/_jsx(RotateCw,{className:\"h-3 w-3\"}),/*#__PURE__*/_jsxs(\"span\",{children:[\"Pasukimas: \",currentRotation,\"\\xB0\"]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-4 gap-1\",children:rotationOptions.map(rotation=>/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onGridRotationChange(rotation),className:`px-2 py-1 text-xs rounded-md transition-all duration-300 ${currentRotation===rotation?'bg-gradient-to-r from-purple-500/60 to-pink-500/60 text-white shadow-lg backdrop-blur-md border border-purple-400/40':'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'}`,children:[rotation,\"\\xB0\"]},rotation))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-white/70 mb-2\",children:\"Spalva\"}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-2 gap-2\",children:colorOptions.map(color=>/*#__PURE__*/_jsx(\"button\",{onClick:()=>onGridColorChange(color.value),className:`px-2 sm:px-3 py-2 text-xs rounded-md transition-all duration-300 border backdrop-blur-md ${currentColor===color.value?'bg-gradient-to-r from-green-500/60 to-emerald-500/60 text-white shadow-lg border-green-400/40':'bg-white/10 hover:bg-white/20 text-white/80 border-white/20'}`,children:color.name},color.value))})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{className:\"block text-xs font-medium text-white/70 mb-2\",children:\"Greitieji nustatymai\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-2 gap-2\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>{onGridSizeChange(120);onGridRotationChange(0);onGridColorChange('rgba(147, 51, 234, 0.25)');},className:\"px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-blue-500/20 to-indigo-500/20 hover:from-blue-500/30 hover:to-indigo-500/30 text-white/90 rounded-md border border-blue-400/30 backdrop-blur-md transition-all duration-300\",children:\"Klasikinis\"}),/*#__PURE__*/_jsx(\"button\",{onClick:()=>{onGridSizeChange(80);onGridRotationChange(45);onGridColorChange('rgba(196, 181, 253, 0.15)');},className:\"px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 text-white/90 rounded-md border border-purple-400/30 backdrop-blur-md transition-all duration-300\",children:\"Modernus\"})]})]})]})})]});};export default GridControls;", "map": {"version": 3, "names": ["React", "useState", "Settings", "Grid", "RotateCw", "jsx", "_jsx", "jsxs", "_jsxs", "GridControls", "_ref", "onGridSizeChange", "onGridRotationChange", "onGridColorChange", "currentSize", "currentRotation", "currentColor", "isOpen", "setIsOpen", "sizeOptions", "rotationOptions", "colorOptions", "name", "value", "className", "children", "onClick", "title", "map", "size", "rotation", "color"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/GridControls.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Settings, Grid, RotateCw } from 'lucide-react';\n\ninterface GridControlsProps {\n  onGridSizeChange: (size: number) => void;\n  onGridRotationChange: (rotation: number) => void;\n  onGridColorChange: (color: string) => void;\n  currentSize: number;\n  currentRotation: number;\n  currentColor: string;\n}\n\nconst GridControls: React.FC<GridControlsProps> = ({\n  onGridSizeChange,\n  onGridRotationChange,\n  onGridColorChange,\n  currentSize,\n  currentRotation,\n  currentColor\n}) => {\n  const [isOpen, setIsOpen] = useState(false);\n\n  const sizeOptions = [40, 60, 80, 100, 120, 140, 160, 180, 200, 220, 240];\n  const rotationOptions = [0, 15, 30, 45, 60, 75, 90, 105, 120, 135, 150, 165, 180];\n  const colorOptions = [\n    { name: '<PERSON><PERSON><PERSON>', value: 'rgba(196, 181, 253, 0.15)' },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 'rgba(147, 51, 234, 0.25)' },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', value: 'rgba(147, 51, 234, 0.40)' },\n    { name: '<PERSON>sus', value: 'rgba(88, 28, 135, 0.35)' }\n  ];\n\n  return (\n    <div className=\"fixed bottom-4 right-4 z-50\">\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=\"bg-gradient-to-r from-white/15 via-white/10 to-white/15 backdrop-blur-xl border border-white/20 rounded-lg sm:rounded-xl p-2 sm:p-3 shadow-2xl hover:shadow-3xl transition-all duration-300\"\n        title=\"Tinklelio nustatymai\"\n      >\n        <Settings className=\"h-4 w-4 sm:h-5 sm:w-5 text-white/80\" />\n      </button>\n\n      {isOpen && (\n        <div className=\"absolute bottom-16 right-0 bg-gradient-to-br from-white/10 via-white/8 to-white/10 backdrop-blur-2xl border border-white/20 rounded-xl sm:rounded-2xl shadow-2xl p-3 sm:p-4 min-w-[240px] sm:min-w-[280px]\">\n          <div className=\"space-y-3 sm:space-y-4\">\n            <h3 className=\"text-sm font-semibold text-white/90 flex items-center space-x-2\">\n                              <Grid className=\"h-4 w-4\" />\n              <span>Tinklelio nustatymai</span>\n            </h3>\n\n            {/* Grid Size Control */}\n            <div>\n              <label className=\"block text-xs font-medium text-white/70 mb-2\">\n                Tinklelio dydis: {currentSize}px\n              </label>\n              <div className=\"grid grid-cols-4 gap-1\">\n                {sizeOptions.map((size) => (\n                  <button\n                    key={size}\n                    onClick={() => onGridSizeChange(size)}\n                    className={`px-2 py-1 text-xs rounded-md transition-all duration-300 ${\n                      currentSize === size\n                        ? 'bg-gradient-to-r from-blue-500/60 to-indigo-500/60 text-white shadow-lg backdrop-blur-md border border-blue-400/40'\n                        : 'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'\n                    }`}\n                  >\n                    {size}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Grid Rotation Control */}\n            <div>\n              <label className=\"block text-xs font-medium text-white/70 mb-2 flex items-center space-x-1\">\n                <RotateCw className=\"h-3 w-3\" />\n                <span>Pasukimas: {currentRotation}°</span>\n              </label>\n              <div className=\"grid grid-cols-4 gap-1\">\n                {rotationOptions.map((rotation) => (\n                  <button\n                    key={rotation}\n                    onClick={() => onGridRotationChange(rotation)}\n                    className={`px-2 py-1 text-xs rounded-md transition-all duration-300 ${\n                      currentRotation === rotation\n                        ? 'bg-gradient-to-r from-purple-500/60 to-pink-500/60 text-white shadow-lg backdrop-blur-md border border-purple-400/40'\n                        : 'bg-white/10 hover:bg-white/20 text-white/80 border border-white/20 backdrop-blur-md'\n                    }`}\n                  >\n                    {rotation}°\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Grid Color Control */}\n            <div>\n              <label className=\"block text-xs font-medium text-white/70 mb-2\">\n                Spalva\n              </label>\n              <div className=\"grid grid-cols-2 gap-2\">\n                {colorOptions.map((color) => (\n                  <button\n                    key={color.value}\n                    onClick={() => onGridColorChange(color.value)}\n                    className={`px-2 sm:px-3 py-2 text-xs rounded-md transition-all duration-300 border backdrop-blur-md ${\n                      currentColor === color.value\n                        ? 'bg-gradient-to-r from-green-500/60 to-emerald-500/60 text-white shadow-lg border-green-400/40'\n                        : 'bg-white/10 hover:bg-white/20 text-white/80 border-white/20'\n                    }`}\n                  >\n                    {color.name}\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Quick Presets */}\n            <div>\n              <label className=\"block text-xs font-medium text-white/70 mb-2\">\n                Greitieji nustatymai\n              </label>\n              <div className=\"grid grid-cols-2 gap-2\">\n                <button\n                  onClick={() => {\n                    onGridSizeChange(120);\n                    onGridRotationChange(0);\n                    onGridColorChange('rgba(147, 51, 234, 0.25)');\n                  }}\n                  className=\"px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-blue-500/20 to-indigo-500/20 hover:from-blue-500/30 hover:to-indigo-500/30 text-white/90 rounded-md border border-blue-400/30 backdrop-blur-md transition-all duration-300\"\n                >\n                  Klasikinis\n                </button>\n                <button\n                  onClick={() => {\n                    onGridSizeChange(80);\n                    onGridRotationChange(45);\n                    onGridColorChange('rgba(196, 181, 253, 0.15)');\n                  }}\n                  className=\"px-2 sm:px-3 py-2 text-xs bg-gradient-to-r from-purple-500/20 to-pink-500/20 hover:from-purple-500/30 hover:to-pink-500/30 text-white/90 rounded-md border border-purple-400/30 backdrop-blur-md transition-all duration-300\"\n                >\n                  Modernus\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default GridControls; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,QAAQ,CAAEC,IAAI,CAAEC,QAAQ,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAWxD,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAO5C,IAP6C,CACjDC,gBAAgB,CAChBC,oBAAoB,CACpBC,iBAAiB,CACjBC,WAAW,CACXC,eAAe,CACfC,YACF,CAAC,CAAAN,IAAA,CACC,KAAM,CAACO,MAAM,CAAEC,SAAS,CAAC,CAAGjB,QAAQ,CAAC,KAAK,CAAC,CAE3C,KAAM,CAAAkB,WAAW,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACxE,KAAM,CAAAC,eAAe,CAAG,CAAC,CAAC,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACjF,KAAM,CAAAC,YAAY,CAAG,CACnB,CAAEC,IAAI,CAAE,SAAS,CAAEC,KAAK,CAAE,2BAA4B,CAAC,CACvD,CAAED,IAAI,CAAE,WAAW,CAAEC,KAAK,CAAE,0BAA2B,CAAC,CACxD,CAAED,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,0BAA2B,CAAC,CACrD,CAAED,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,yBAA0B,CAAC,CACrD,CAED,mBACEf,KAAA,QAAKgB,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CnB,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAMR,SAAS,CAAC,CAACD,MAAM,CAAE,CAClCO,SAAS,CAAC,6LAA6L,CACvMG,KAAK,CAAC,sBAAsB,CAAAF,QAAA,cAE5BnB,IAAA,CAACJ,QAAQ,EAACsB,SAAS,CAAC,qCAAqC,CAAE,CAAC,CACtD,CAAC,CAERP,MAAM,eACLX,IAAA,QAAKkB,SAAS,CAAC,4MAA4M,CAAAC,QAAA,cACzNjB,KAAA,QAAKgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCjB,KAAA,OAAIgB,SAAS,CAAC,iEAAiE,CAAAC,QAAA,eAC7DnB,IAAA,CAACH,IAAI,EAACqB,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5ClB,IAAA,SAAAmB,QAAA,CAAM,sBAAoB,CAAM,CAAC,EAC/B,CAAC,cAGLjB,KAAA,QAAAiB,QAAA,eACEjB,KAAA,UAAOgB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,EAAC,mBAC7C,CAACX,WAAW,CAAC,IAChC,EAAO,CAAC,cACRR,IAAA,QAAKkB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACpCN,WAAW,CAACS,GAAG,CAAEC,IAAI,eACpBvB,IAAA,WAEEoB,OAAO,CAAEA,CAAA,GAAMf,gBAAgB,CAACkB,IAAI,CAAE,CACtCL,SAAS,CAAE,4DACTV,WAAW,GAAKe,IAAI,CAChB,oHAAoH,CACpH,qFAAqF,EACxF,CAAAJ,QAAA,CAEFI,IAAI,EARAA,IASC,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNrB,KAAA,QAAAiB,QAAA,eACEjB,KAAA,UAAOgB,SAAS,CAAC,0EAA0E,CAAAC,QAAA,eACzFnB,IAAA,CAACF,QAAQ,EAACoB,SAAS,CAAC,SAAS,CAAE,CAAC,cAChChB,KAAA,SAAAiB,QAAA,EAAM,aAAW,CAACV,eAAe,CAAC,MAAC,EAAM,CAAC,EACrC,CAAC,cACRT,IAAA,QAAKkB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACpCL,eAAe,CAACQ,GAAG,CAAEE,QAAQ,eAC5BtB,KAAA,WAEEkB,OAAO,CAAEA,CAAA,GAAMd,oBAAoB,CAACkB,QAAQ,CAAE,CAC9CN,SAAS,CAAE,4DACTT,eAAe,GAAKe,QAAQ,CACxB,sHAAsH,CACtH,qFAAqF,EACxF,CAAAL,QAAA,EAEFK,QAAQ,CAAC,MACZ,GATOA,QASC,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNtB,KAAA,QAAAiB,QAAA,eACEnB,IAAA,UAAOkB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,QAEhE,CAAO,CAAC,cACRnB,IAAA,QAAKkB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,CACpCJ,YAAY,CAACO,GAAG,CAAEG,KAAK,eACtBzB,IAAA,WAEEoB,OAAO,CAAEA,CAAA,GAAMb,iBAAiB,CAACkB,KAAK,CAACR,KAAK,CAAE,CAC9CC,SAAS,CAAE,4FACTR,YAAY,GAAKe,KAAK,CAACR,KAAK,CACxB,+FAA+F,CAC/F,6DAA6D,EAChE,CAAAE,QAAA,CAEFM,KAAK,CAACT,IAAI,EARNS,KAAK,CAACR,KASL,CACT,CAAC,CACC,CAAC,EACH,CAAC,cAGNf,KAAA,QAAAiB,QAAA,eACEnB,IAAA,UAAOkB,SAAS,CAAC,8CAA8C,CAAAC,QAAA,CAAC,sBAEhE,CAAO,CAAC,cACRjB,KAAA,QAAKgB,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCnB,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAM,CACbf,gBAAgB,CAAC,GAAG,CAAC,CACrBC,oBAAoB,CAAC,CAAC,CAAC,CACvBC,iBAAiB,CAAC,0BAA0B,CAAC,CAC/C,CAAE,CACFW,SAAS,CAAC,4NAA4N,CAAAC,QAAA,CACvO,YAED,CAAQ,CAAC,cACTnB,IAAA,WACEoB,OAAO,CAAEA,CAAA,GAAM,CACbf,gBAAgB,CAAC,EAAE,CAAC,CACpBC,oBAAoB,CAAC,EAAE,CAAC,CACxBC,iBAAiB,CAAC,2BAA2B,CAAC,CAChD,CAAE,CACFW,SAAS,CAAC,8NAA8N,CAAAC,QAAA,CACzO,UAED,CAAQ,CAAC,EACN,CAAC,EACH,CAAC,EACH,CAAC,CACH,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAhB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}