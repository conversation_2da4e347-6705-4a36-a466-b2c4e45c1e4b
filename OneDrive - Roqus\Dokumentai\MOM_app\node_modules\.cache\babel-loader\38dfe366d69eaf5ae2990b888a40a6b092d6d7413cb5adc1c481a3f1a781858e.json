{"ast": null, "code": "import React,{useState}from'react';import{FileText,Trash2,Mic,Square,Loader2,List}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs,Fragment as _Fragment}from\"react/jsx-runtime\";export const MeetingsList=_ref=>{let{meetings,onSelectMeeting,onDeleteMeeting,activeView}=_ref;const[expandedMeeting,setExpandedMeeting]=useState(null);const formatDuration=seconds=>{const mins=Math.floor(seconds/60);const secs=seconds%60;return`${mins}:${secs.toString().padStart(2,'0')}`;};const getStatusIcon=status=>{switch(status){case'recording':return/*#__PURE__*/_jsx(Mic,{className:\"h-3 w-3 text-red-500 animate-pulse\"});case'processing':return/*#__PURE__*/_jsx(Loader2,{className:\"h-3 w-3 text-blue-500 animate-spin\"});case'completed':return/*#__PURE__*/_jsx(FileText,{className:\"h-3 w-3 text-green-500\"});case'error':return/*#__PURE__*/_jsx(Square,{className:\"h-3 w-3 text-red-500\"});default:return/*#__PURE__*/_jsx(FileText,{className:\"h-3 w-3 text-gray-400\"});}};const getStatusText=status=>{switch(status){case'recording':return'Įrašoma';case'processing':return'Apdorojama';case'completed':return'Baigta';case'error':return'Klaida';default:return'Nežinoma';}};const getStatusColor=status=>{switch(status){case'recording':return'text-red-500';case'processing':return'text-blue-500';case'completed':return'text-green-500';case'error':return'text-red-500';default:return'text-gray-400';}};if(meetings.length===0){return/*#__PURE__*/_jsxs(\"div\",{className:\"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 h-full transition-smooth hover:shadow-primary float-effect\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-between mb-4\",children:/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-gray-900\",children:\"Pokalbiai\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center h-64 text-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mb-3 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\",children:/*#__PURE__*/_jsx(FileText,{className:\"h-6 w-6 text-gray-400\"})}),/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-gray-900 mb-1\",children:\"N\\u0117ra pokalbi\\u0173\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:\"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"})]})]});}return/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col animate-fade-in\",children:meetings.length>0?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-4 overflow-y-auto\",children:meetings.map((meeting,index)=>/*#__PURE__*/_jsx(\"div\",{className:\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] animate-fade-in-up\",style:{animationDelay:`${index*50}ms`},onClick:()=>onSelectMeeting(meeting),children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"h4\",{className:\"text-lg font-semibold text-white truncate transition-colors duration-200 mb-2\",children:meeting.title}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base text-white/70 mb-3 transition-colors duration-200\",children:meeting.date.toLocaleString('lt-LT')}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-4 mb-3 text-sm\",children:[/*#__PURE__*/_jsxs(\"span\",{className:\"text-white/60 transition-colors duration-200\",children:[Math.floor(meeting.duration/60),\":\",String(meeting.duration%60).padStart(2,'0')]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[getStatusIcon(meeting.transcriptionStatus.state),/*#__PURE__*/_jsx(\"span\",{className:`transition-colors duration-200 ${getStatusColor(meeting.transcriptionStatus.state)}`,children:getStatusText(meeting.transcriptionStatus.state)})]})]}),meeting.transcript&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 text-sm text-green-400 transition-all duration-200\",children:[/*#__PURE__*/_jsx(FileText,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsxs(\"span\",{children:[meeting.transcript.length,\" segment\\u0173\"]}),meeting.participants&&/*#__PURE__*/_jsxs(_Fragment,{children:[/*#__PURE__*/_jsx(\"span\",{children:\"\\u2022\"}),/*#__PURE__*/_jsxs(\"span\",{children:[meeting.participants.length,\" dalyvi\\u0173\"]})]})]})]}),/*#__PURE__*/_jsx(\"button\",{onClick:e=>{e.stopPropagation();onDeleteMeeting(meeting.id);},className:\"p-3 text-white/40 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 ml-3 transform hover:scale-110\",title:\"I\\u0161trinti pokalb\\u012F\",children:/*#__PURE__*/_jsx(Trash2,{className:\"h-5 w-5\"})})]})},meeting.id))}):/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center animate-pulse\",children:/*#__PURE__*/_jsx(List,{className:\"h-10 w-10 text-indigo-400\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-xl sm:text-2xl font-semibold text-white\",children:\"N\\u0117ra pokalbi\\u0173\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base sm:text-lg text-white/70\",children:\"Prad\\u0117kite nauj\\u0105 pokalb\\u012F, kad pamatytum\\u0117te j\\u012F \\u010Dia\"})]})]})});};", "map": {"version": 3, "names": ["React", "useState", "FileText", "Trash2", "Mic", "Square", "Loader2", "List", "jsx", "_jsx", "jsxs", "_jsxs", "Fragment", "_Fragment", "MeetingsList", "_ref", "meetings", "onSelectMeeting", "onDeleteMeeting", "activeView", "expandedMeeting", "setExpandedMeeting", "formatDuration", "seconds", "mins", "Math", "floor", "secs", "toString", "padStart", "getStatusIcon", "status", "className", "getStatusText", "getStatusColor", "length", "children", "map", "meeting", "index", "style", "animationDelay", "onClick", "title", "date", "toLocaleString", "duration", "String", "transcriptionStatus", "state", "transcript", "participants", "e", "stopPropagation", "id"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/MeetingsList.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { Calendar, Clock, FileText, Trash2, Download, Mic, Square, Loader2, Volume2, List } from 'lucide-react';\nimport { Meeting } from '../types/meeting';\nimport { AudioPlayer } from './AudioPlayer';\n\ninterface MeetingsListProps {\n  meetings: Meeting[];\n  currentMeeting: Meeting | null;\n  onSelectMeeting: (meeting: Meeting) => void;\n  onDeleteMeeting: (meetingId: string) => void;\n  onExportMeeting: (meeting: Meeting) => void;\n  isRecording?: boolean;\n  activeView: 'list' | 'grid';\n}\n\nexport const MeetingsList: React.FC<MeetingsListProps> = ({\n  meetings,\n  onSelectMeeting,\n  onDeleteMeeting,\n  activeView,\n}) => {\n  const [expandedMeeting, setExpandedMeeting] = useState<string | null>(null);\n  const formatDuration = (seconds: number): string => {\n    const mins = Math.floor(seconds / 60);\n    const secs = seconds % 60;\n    return `${mins}:${secs.toString().padStart(2, '0')}`;\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return <Mic className=\"h-3 w-3 text-red-500 animate-pulse\" />;\n      case 'processing':\n        return <Loader2 className=\"h-3 w-3 text-blue-500 animate-spin\" />;\n      case 'completed':\n        return <FileText className=\"h-3 w-3 text-green-500\" />;\n      case 'error':\n        return <Square className=\"h-3 w-3 text-red-500\" />;\n      default:\n        return <FileText className=\"h-3 w-3 text-gray-400\" />;\n    }\n  };\n\n  const getStatusText = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return 'Įrašoma';\n      case 'processing':\n        return 'Apdorojama';\n      case 'completed':\n        return 'Baigta';\n      case 'error':\n        return 'Klaida';\n      default:\n        return 'Nežinoma';\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'recording':\n        return 'text-red-500';\n      case 'processing':\n        return 'text-blue-500';\n      case 'completed':\n        return 'text-green-500';\n      case 'error':\n        return 'text-red-500';\n      default:\n        return 'text-gray-400';\n    }\n  };\n\n  if (meetings.length === 0) {\n    return (\n      <div className=\"gradient-border-fade rounded-3xl shadow-elegant bg-unique-gradient-1 backdrop-blur-2xl p-6 h-full transition-smooth hover:shadow-primary float-effect\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Pokalbiai</h2>\n        </div>\n        <div className=\"flex flex-col items-center justify-center h-64 text-center\">\n          <div className=\"w-12 h-12 bg-gradient-to-br from-indigo-100/80 via-blue-100/60 to-purple-100/80 rounded-full flex items-center justify-center mb-3 border border-gradient-to-r from-indigo-200/40 to-purple-200/40 shadow-soft\">\n            <FileText className=\"h-6 w-6 text-gray-400\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900 mb-1\">Nėra pokalbių</h3>\n          <p className=\"text-xs text-gray-500\">Pradėkite naują pokalbį, kad pamatytumėte jį čia</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"flex-1 flex flex-col animate-fade-in\">\n      {meetings.length > 0 ? (\n        <div className=\"space-y-4 overflow-y-auto\">\n          {meetings.map((meeting, index) => (\n            <div\n              key={meeting.id}\n              className=\"bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-all duration-300 cursor-pointer transform hover:scale-[1.02] animate-fade-in-up\"\n              style={{ animationDelay: `${index * 50}ms` }}\n              onClick={() => onSelectMeeting(meeting)}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-lg font-semibold text-white truncate transition-colors duration-200 mb-2\">{meeting.title}</h4>\n                  <p className=\"text-base text-white/70 mb-3 transition-colors duration-200\">\n                    {meeting.date.toLocaleString('lt-LT')}\n                  </p>\n                  <div className=\"flex items-center space-x-4 mb-3 text-sm\">\n                    <span className=\"text-white/60 transition-colors duration-200\">\n                      {Math.floor(meeting.duration / 60)}:{String(meeting.duration % 60).padStart(2, '0')}\n                    </span>\n                    <div className=\"flex items-center space-x-2\">\n                      {getStatusIcon(meeting.transcriptionStatus.state)}\n                      <span className={`transition-colors duration-200 ${getStatusColor(meeting.transcriptionStatus.state)}`}>\n                        {getStatusText(meeting.transcriptionStatus.state)}\n                      </span>\n                    </div>\n                  </div>\n                  {meeting.transcript && (\n                    <div className=\"flex items-center space-x-3 text-sm text-green-400 transition-all duration-200\">\n                      <FileText className=\"h-4 w-4\" />\n                      <span>{meeting.transcript.length} segmentų</span>\n                      {meeting.participants && (\n                        <>\n                          <span>•</span>\n                          <span>{meeting.participants.length} dalyvių</span>\n                        </>\n                      )}\n                    </div>\n                  )}\n                </div>\n                <button\n                  onClick={(e) => {\n                    e.stopPropagation();\n                    onDeleteMeeting(meeting.id);\n                  }}\n                  className=\"p-3 text-white/40 hover:text-red-400 hover:bg-red-500/10 rounded-lg transition-all duration-200 ml-3 transform hover:scale-110\"\n                  title=\"Ištrinti pokalbį\"\n                >\n                  <Trash2 className=\"h-5 w-5\" />\n                </button>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <div className=\"flex-1 flex flex-col items-center justify-center text-center space-y-6 animate-fade-in-up\">\n          <div className=\"w-20 h-20 bg-gradient-to-br from-indigo-500/20 to-purple-500/20 rounded-full flex items-center justify-center animate-pulse\">\n            <List className=\"h-10 w-10 text-indigo-400\" />\n          </div>\n          <div>\n            <h3 className=\"text-xl sm:text-2xl font-semibold text-white\">Nėra pokalbių</h3>\n            <p className=\"text-base sm:text-lg text-white/70\">\n              Pradėkite naują pokalbį, kad pamatytumėte jį čia\n            </p>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAA0BC,QAAQ,CAAEC,MAAM,CAAYC,GAAG,CAAEC,MAAM,CAAEC,OAAO,CAAWC,IAAI,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,CAAAC,QAAA,IAAAC,SAAA,yBAchH,MAAO,MAAM,CAAAC,YAAyC,CAAGC,IAAA,EAKnD,IALoD,CACxDC,QAAQ,CACRC,eAAe,CACfC,eAAe,CACfC,UACF,CAAC,CAAAJ,IAAA,CACC,KAAM,CAACK,eAAe,CAAEC,kBAAkB,CAAC,CAAGpB,QAAQ,CAAgB,IAAI,CAAC,CAC3E,KAAM,CAAAqB,cAAc,CAAIC,OAAe,EAAa,CAClD,KAAM,CAAAC,IAAI,CAAGC,IAAI,CAACC,KAAK,CAACH,OAAO,CAAG,EAAE,CAAC,CACrC,KAAM,CAAAI,IAAI,CAAGJ,OAAO,CAAG,EAAE,CACzB,MAAO,GAAGC,IAAI,IAAIG,IAAI,CAACC,QAAQ,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAAE,CACtD,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIC,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,mBAAOtB,IAAA,CAACL,GAAG,EAAC4B,SAAS,CAAC,oCAAoC,CAAE,CAAC,CAC/D,IAAK,YAAY,CACf,mBAAOvB,IAAA,CAACH,OAAO,EAAC0B,SAAS,CAAC,oCAAoC,CAAE,CAAC,CACnE,IAAK,WAAW,CACd,mBAAOvB,IAAA,CAACP,QAAQ,EAAC8B,SAAS,CAAC,wBAAwB,CAAE,CAAC,CACxD,IAAK,OAAO,CACV,mBAAOvB,IAAA,CAACJ,MAAM,EAAC2B,SAAS,CAAC,sBAAsB,CAAE,CAAC,CACpD,QACE,mBAAOvB,IAAA,CAACP,QAAQ,EAAC8B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CACzD,CACF,CAAC,CAED,KAAM,CAAAC,aAAa,CAAIF,MAAc,EAAK,CACxC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,SAAS,CAClB,IAAK,YAAY,CACf,MAAO,YAAY,CACrB,IAAK,WAAW,CACd,MAAO,QAAQ,CACjB,IAAK,OAAO,CACV,MAAO,QAAQ,CACjB,QACE,MAAO,UAAU,CACrB,CACF,CAAC,CAED,KAAM,CAAAG,cAAc,CAAIH,MAAc,EAAK,CACzC,OAAQA,MAAM,EACZ,IAAK,WAAW,CACd,MAAO,cAAc,CACvB,IAAK,YAAY,CACf,MAAO,eAAe,CACxB,IAAK,WAAW,CACd,MAAO,gBAAgB,CACzB,IAAK,OAAO,CACV,MAAO,cAAc,CACvB,QACE,MAAO,eAAe,CAC1B,CACF,CAAC,CAED,GAAIf,QAAQ,CAACmB,MAAM,GAAK,CAAC,CAAE,CACzB,mBACExB,KAAA,QAAKqB,SAAS,CAAC,uJAAuJ,CAAAI,QAAA,eACpK3B,IAAA,QAAKuB,SAAS,CAAC,wCAAwC,CAAAI,QAAA,cACrD3B,IAAA,OAAIuB,SAAS,CAAC,qCAAqC,CAAAI,QAAA,CAAC,WAAS,CAAI,CAAC,CAC/D,CAAC,cACNzB,KAAA,QAAKqB,SAAS,CAAC,4DAA4D,CAAAI,QAAA,eACzE3B,IAAA,QAAKuB,SAAS,CAAC,gNAAgN,CAAAI,QAAA,cAC7N3B,IAAA,CAACP,QAAQ,EAAC8B,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC3C,CAAC,cACNvB,IAAA,OAAIuB,SAAS,CAAC,wCAAwC,CAAAI,QAAA,CAAC,yBAAa,CAAI,CAAC,cACzE3B,IAAA,MAAGuB,SAAS,CAAC,uBAAuB,CAAAI,QAAA,CAAC,gFAAgD,CAAG,CAAC,EACtF,CAAC,EACH,CAAC,CAEV,CAEA,mBACE3B,IAAA,QAAKuB,SAAS,CAAC,sCAAsC,CAAAI,QAAA,CAClDpB,QAAQ,CAACmB,MAAM,CAAG,CAAC,cAClB1B,IAAA,QAAKuB,SAAS,CAAC,2BAA2B,CAAAI,QAAA,CACvCpB,QAAQ,CAACqB,GAAG,CAAC,CAACC,OAAO,CAAEC,KAAK,gBAC3B9B,IAAA,QAEEuB,SAAS,CAAC,gLAAgL,CAC1LQ,KAAK,CAAE,CAAEC,cAAc,CAAE,GAAGF,KAAK,CAAG,EAAE,IAAK,CAAE,CAC7CG,OAAO,CAAEA,CAAA,GAAMzB,eAAe,CAACqB,OAAO,CAAE,CAAAF,QAAA,cAExCzB,KAAA,QAAKqB,SAAS,CAAC,kCAAkC,CAAAI,QAAA,eAC/CzB,KAAA,QAAKqB,SAAS,CAAC,gBAAgB,CAAAI,QAAA,eAC7B3B,IAAA,OAAIuB,SAAS,CAAC,+EAA+E,CAAAI,QAAA,CAAEE,OAAO,CAACK,KAAK,CAAK,CAAC,cAClHlC,IAAA,MAAGuB,SAAS,CAAC,6DAA6D,CAAAI,QAAA,CACvEE,OAAO,CAACM,IAAI,CAACC,cAAc,CAAC,OAAO,CAAC,CACpC,CAAC,cACJlC,KAAA,QAAKqB,SAAS,CAAC,0CAA0C,CAAAI,QAAA,eACvDzB,KAAA,SAAMqB,SAAS,CAAC,8CAA8C,CAAAI,QAAA,EAC3DX,IAAI,CAACC,KAAK,CAACY,OAAO,CAACQ,QAAQ,CAAG,EAAE,CAAC,CAAC,GAAC,CAACC,MAAM,CAACT,OAAO,CAACQ,QAAQ,CAAG,EAAE,CAAC,CAACjB,QAAQ,CAAC,CAAC,CAAE,GAAG,CAAC,EAC/E,CAAC,cACPlB,KAAA,QAAKqB,SAAS,CAAC,6BAA6B,CAAAI,QAAA,EACzCN,aAAa,CAACQ,OAAO,CAACU,mBAAmB,CAACC,KAAK,CAAC,cACjDxC,IAAA,SAAMuB,SAAS,CAAE,kCAAkCE,cAAc,CAACI,OAAO,CAACU,mBAAmB,CAACC,KAAK,CAAC,EAAG,CAAAb,QAAA,CACpGH,aAAa,CAACK,OAAO,CAACU,mBAAmB,CAACC,KAAK,CAAC,CAC7C,CAAC,EACJ,CAAC,EACH,CAAC,CACLX,OAAO,CAACY,UAAU,eACjBvC,KAAA,QAAKqB,SAAS,CAAC,gFAAgF,CAAAI,QAAA,eAC7F3B,IAAA,CAACP,QAAQ,EAAC8B,SAAS,CAAC,SAAS,CAAE,CAAC,cAChCrB,KAAA,SAAAyB,QAAA,EAAOE,OAAO,CAACY,UAAU,CAACf,MAAM,CAAC,gBAAS,EAAM,CAAC,CAChDG,OAAO,CAACa,YAAY,eACnBxC,KAAA,CAAAE,SAAA,EAAAuB,QAAA,eACE3B,IAAA,SAAA2B,QAAA,CAAM,QAAC,CAAM,CAAC,cACdzB,KAAA,SAAAyB,QAAA,EAAOE,OAAO,CAACa,YAAY,CAAChB,MAAM,CAAC,eAAQ,EAAM,CAAC,EAClD,CACH,EACE,CACN,EACE,CAAC,cACN1B,IAAA,WACEiC,OAAO,CAAGU,CAAC,EAAK,CACdA,CAAC,CAACC,eAAe,CAAC,CAAC,CACnBnC,eAAe,CAACoB,OAAO,CAACgB,EAAE,CAAC,CAC7B,CAAE,CACFtB,SAAS,CAAC,gIAAgI,CAC1IW,KAAK,CAAC,4BAAkB,CAAAP,QAAA,cAExB3B,IAAA,CAACN,MAAM,EAAC6B,SAAS,CAAC,SAAS,CAAE,CAAC,CACxB,CAAC,EACN,CAAC,EA7CDM,OAAO,CAACgB,EA8CV,CACN,CAAC,CACC,CAAC,cAEN3C,KAAA,QAAKqB,SAAS,CAAC,2FAA2F,CAAAI,QAAA,eACxG3B,IAAA,QAAKuB,SAAS,CAAC,6HAA6H,CAAAI,QAAA,cAC1I3B,IAAA,CAACF,IAAI,EAACyB,SAAS,CAAC,2BAA2B,CAAE,CAAC,CAC3C,CAAC,cACNrB,KAAA,QAAAyB,QAAA,eACE3B,IAAA,OAAIuB,SAAS,CAAC,8CAA8C,CAAAI,QAAA,CAAC,yBAAa,CAAI,CAAC,cAC/E3B,IAAA,MAAGuB,SAAS,CAAC,oCAAoC,CAAAI,QAAA,CAAC,gFAElD,CAAG,CAAC,EACD,CAAC,EACH,CACN,CACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}