{"ast": null, "code": "import React from'react';import{Plus,Mic2,Square}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const RecordingPanel=_ref=>{let{recordingState,onStartRecording,onStopRecording}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 flex flex-col justify-center items-center space-y-8 p-4 animate-fade-in\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center space-y-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:`w-28 h-28 sm:w-36 sm:h-36 rounded-full flex items-center justify-center shadow-2xl transition-all duration-700 ease-out transform ${recordingState.isRecording?'bg-gradient-to-br from-red-500/80 to-red-600/80 animate-pulse scale-110':'bg-gradient-to-br from-blue-500/80 to-indigo-600/80 scale-100 hover:scale-105'}`,children:/*#__PURE__*/_jsx(Mic2,{className:`h-14 w-14 sm:h-18 sm:w-18 text-white transition-all duration-500 ${recordingState.isRecording?'animate-bounce':'hover:scale-110'}`})}),/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3 animate-fade-in-up\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-2xl sm:text-3xl font-bold text-white transition-all duration-300\",children:recordingState.isRecording?'Įrašoma...':'Pasiruošęs įrašyti'}),/*#__PURE__*/_jsx(\"p\",{className:\"text-base sm:text-lg text-white/70 max-w-lg transition-all duration-300 leading-relaxed\",children:recordingState.isRecording?'Spauskite \"Sustabdyti\" norėdami baigti įrašymą':'Spauskite \"Naujas pokalbis\" norėdami pradėti naują audio įrašymą su automatine transkribavimo galimybe.'})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-200\",children:!recordingState.isRecording?/*#__PURE__*/_jsxs(\"button\",{onClick:()=>onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`),className:\"inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\",children:[/*#__PURE__*/_jsx(Plus,{className:\"h-6 w-6 transition-transform duration-200 group-hover:rotate-90\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Naujas pokalbis\"})]}):/*#__PURE__*/_jsxs(\"button\",{onClick:onStopRecording,className:\"inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-red-500/80 via-red-600/70 to-red-700/80 hover:from-red-500/90 hover:via-red-600/80 hover:to-red-700/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-red-400/40 hover:border-red-300/50 transform hover:scale-105 active:scale-95\",children:[/*#__PURE__*/_jsx(Square,{className:\"h-6 w-6\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Sustabdyti \\u012Fra\\u0161ym\\u0105\"})]})}),recordingState.isRecording&&/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 text-red-400 animate-fade-in-up animation-delay-400\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 bg-red-400 rounded-full animate-pulse\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-base font-medium\",children:\"\\u012Era\\u0161oma...\"})]})]});};", "map": {"version": 3, "names": ["React", "Plus", "Mic2", "Square", "jsx", "_jsx", "jsxs", "_jsxs", "RecordingPanel", "_ref", "recordingState", "onStartRecording", "onStopRecording", "className", "children", "isRecording", "onClick", "Date", "toLocaleString"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/RecordingPanel.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\r\nimport { Plus, Mic2, Square, Pause, Play, AlertCircle } from 'lucide-react';\r\nimport { RecordingButton } from './RecordingButton';\r\nimport { RecordingIndicator } from './RecordingIndicator';\r\nimport { Meeting, RecordingState } from '../types/meeting';\r\n\r\ninterface RecordingPanelProps {\r\n  recordingState: RecordingState;\r\n  currentMeeting: Meeting | null;\r\n  onStartRecording: (title: string) => Promise<void>;\r\n  onStopRecording: () => Promise<void>;\r\n  onPauseRecording: () => void;\r\n  onResumeRecording: () => void;\r\n}\r\n\r\nexport const RecordingPanel: React.FC<RecordingPanelProps> = ({\r\n  recordingState,\r\n  onStartRecording,\r\n  onStopRecording,\r\n}) => {\r\n  return (\r\n    <div className=\"flex-1 flex flex-col justify-center items-center space-y-8 p-4 animate-fade-in\">\r\n      {/* Recording Status Display */}\r\n      <div className=\"text-center space-y-6\">\r\n        <div className={`w-28 h-28 sm:w-36 sm:h-36 rounded-full flex items-center justify-center shadow-2xl transition-all duration-700 ease-out transform ${\r\n          recordingState.isRecording \r\n            ? 'bg-gradient-to-br from-red-500/80 to-red-600/80 animate-pulse scale-110' \r\n            : 'bg-gradient-to-br from-blue-500/80 to-indigo-600/80 scale-100 hover:scale-105'\r\n        }`}>\r\n          <Mic2 className={`h-14 w-14 sm:h-18 sm:w-18 text-white transition-all duration-500 ${\r\n            recordingState.isRecording ? 'animate-bounce' : 'hover:scale-110'\r\n          }`} />\r\n        </div>\r\n        \r\n        <div className=\"space-y-3 animate-fade-in-up\">\r\n          <h3 className=\"text-2xl sm:text-3xl font-bold text-white transition-all duration-300\">\r\n            {recordingState.isRecording ? 'Įrašoma...' : 'Pasiruošęs įrašyti'}\r\n          </h3>\r\n          <p className=\"text-base sm:text-lg text-white/70 max-w-lg transition-all duration-300 leading-relaxed\">\r\n            {recordingState.isRecording \r\n              ? 'Spauskite \"Sustabdyti\" norėdami baigti įrašymą'\r\n              : 'Spauskite \"Naujas pokalbis\" norėdami pradėti naują audio įrašymą su automatine transkribavimo galimybe.'\r\n            }\r\n          </p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Recording Controls */}\r\n      <div className=\"flex flex-col sm:flex-row gap-4 animate-fade-in-up animation-delay-200\">\r\n        {!recordingState.isRecording ? (\r\n          <button\r\n            onClick={() => onStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`)}\r\n            className=\"inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-500/80 via-blue-600/70 to-indigo-600/80 hover:from-blue-500/90 hover:via-blue-600/80 hover:to-indigo-600/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-blue-400/40 hover:border-blue-300/50 transform hover:scale-105 active:scale-95\"\r\n          >\r\n            <Plus className=\"h-6 w-6 transition-transform duration-200 group-hover:rotate-90\" />\r\n            <span>Naujas pokalbis</span>\r\n          </button>\r\n        ) : (\r\n          <button\r\n            onClick={onStopRecording}\r\n            className=\"inline-flex items-center justify-center space-x-3 px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-red-500/80 via-red-600/70 to-red-700/80 hover:from-red-500/90 hover:via-red-600/80 hover:to-red-700/90 rounded-xl shadow-xl hover:shadow-2xl transition-all duration-300 backdrop-blur-md border border-red-400/40 hover:border-red-300/50 transform hover:scale-105 active:scale-95\"\r\n          >\r\n            <Square className=\"h-6 w-6\" />\r\n            <span>Sustabdyti įrašymą</span>\r\n          </button>\r\n        )}\r\n      </div>\r\n\r\n      {/* Recording Indicator */}\r\n      {recordingState.isRecording && (\r\n        <div className=\"flex items-center space-x-3 text-red-400 animate-fade-in-up animation-delay-400\">\r\n          <div className=\"w-3 h-3 bg-red-400 rounded-full animate-pulse\"></div>\r\n          <span className=\"text-base font-medium\">Įrašoma...</span>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n}; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAiC,OAAO,CACpD,OAASC,IAAI,CAAEC,IAAI,CAAEC,MAAM,KAAkC,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAc5E,MAAO,MAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIvD,IAJwD,CAC5DC,cAAc,CACdC,gBAAgB,CAChBC,eACF,CAAC,CAAAH,IAAA,CACC,mBACEF,KAAA,QAAKM,SAAS,CAAC,gFAAgF,CAAAC,QAAA,eAE7FP,KAAA,QAAKM,SAAS,CAAC,uBAAuB,CAAAC,QAAA,eACpCT,IAAA,QAAKQ,SAAS,CAAE,qIACdH,cAAc,CAACK,WAAW,CACtB,yEAAyE,CACzE,+EAA+E,EAClF,CAAAD,QAAA,cACDT,IAAA,CAACH,IAAI,EAACW,SAAS,CAAE,oEACfH,cAAc,CAACK,WAAW,CAAG,gBAAgB,CAAG,iBAAiB,EAChE,CAAE,CAAC,CACH,CAAC,cAENR,KAAA,QAAKM,SAAS,CAAC,8BAA8B,CAAAC,QAAA,eAC3CT,IAAA,OAAIQ,SAAS,CAAC,uEAAuE,CAAAC,QAAA,CAClFJ,cAAc,CAACK,WAAW,CAAG,YAAY,CAAG,oBAAoB,CAC/D,CAAC,cACLV,IAAA,MAAGQ,SAAS,CAAC,yFAAyF,CAAAC,QAAA,CACnGJ,cAAc,CAACK,WAAW,CACvB,gDAAgD,CAChD,yGAAyG,CAE5G,CAAC,EACD,CAAC,EACH,CAAC,cAGNV,IAAA,QAAKQ,SAAS,CAAC,wEAAwE,CAAAC,QAAA,CACpF,CAACJ,cAAc,CAACK,WAAW,cAC1BR,KAAA,WACES,OAAO,CAAEA,CAAA,GAAML,gBAAgB,CAAC,YAAY,GAAI,CAAAM,IAAI,CAAC,CAAC,CAACC,cAAc,CAAC,OAAO,CAAC,EAAE,CAAE,CAClFL,SAAS,CAAC,qZAAqZ,CAAAC,QAAA,eAE/ZT,IAAA,CAACJ,IAAI,EAACY,SAAS,CAAC,iEAAiE,CAAE,CAAC,cACpFR,IAAA,SAAAS,QAAA,CAAM,iBAAe,CAAM,CAAC,EACtB,CAAC,cAETP,KAAA,WACES,OAAO,CAAEJ,eAAgB,CACzBC,SAAS,CAAC,yYAAyY,CAAAC,QAAA,eAEnZT,IAAA,CAACF,MAAM,EAACU,SAAS,CAAC,SAAS,CAAE,CAAC,cAC9BR,IAAA,SAAAS,QAAA,CAAM,mCAAkB,CAAM,CAAC,EACzB,CACT,CACE,CAAC,CAGLJ,cAAc,CAACK,WAAW,eACzBR,KAAA,QAAKM,SAAS,CAAC,iFAAiF,CAAAC,QAAA,eAC9FT,IAAA,QAAKQ,SAAS,CAAC,+CAA+C,CAAM,CAAC,cACrER,IAAA,SAAMQ,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,sBAAU,CAAM,CAAC,EACtD,CACN,EACE,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}