{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive - Roqus\\\\Dokumentai\\\\MOM_app\\\\src\\\\components\\\\WhisperStatusIndicator.tsx\";\nimport React from 'react';\nimport { Zap, Check } from 'lucide-react';\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const WhisperStatusIndicator = () => {\n  const isConfigured = isWhisperConfigured();\n  const status = getConfigStatus();\n  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured\n\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"elegant-whisper-status\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: `elegant-status-dot ${isConnected ? 'connected' : 'disconnected'}`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Zap, {\n      className: `h-4 w-4 ${isConnected ? 'text-emerald-300' : 'text-red-300'} transition-colors duration-300`\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n      className: \"text-sm font-medium text-white/90\",\n      children: \"Whisper A-I\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 14,\n      columnNumber: 7\n    }, this), isConnected && /*#__PURE__*/_jsxDEV(Check, {\n      className: \"h-4 w-4 text-emerald-300\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 23\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = WhisperStatusIndicator;\nvar _c;\n$RefreshReg$(_c, \"WhisperStatusIndicator\");", "map": {"version": 3, "names": ["React", "Zap", "Check", "isWhisperConfigured", "getConfigStatus", "jsxDEV", "_jsxDEV", "WhisperStatusIndicator", "isConfigured", "status", "isConnected", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>Circle2, XCircle, <PERSON>ert<PERSON><PERSON>gle, Zap, Check } from 'lucide-react';\r\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\r\n\r\nexport const WhisperStatusIndicator: React.FC = () => {\r\n  const isConfigured = isWhisperConfigured();\r\n  const status = getConfigStatus();\r\n  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured\r\n\r\n  return (\r\n    <div className=\"elegant-whisper-status\">\r\n      <div className={`elegant-status-dot ${isConnected ? 'connected' : 'disconnected'}`}></div>\r\n      <Zap className={`h-4 w-4 ${isConnected ? 'text-emerald-300' : 'text-red-300'} transition-colors duration-300`} />\r\n      <span className=\"text-sm font-medium text-white/90\">Whisper A-I</span>\r\n      {isConnected && <Check className=\"h-4 w-4 text-emerald-300\" />}\r\n    </div>\r\n  );\r\n};"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAA+CC,GAAG,EAAEC,KAAK,QAAQ,cAAc;AAC/E,SAASC,mBAAmB,EAAEC,eAAe,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzE,OAAO,MAAMC,sBAAgC,GAAGA,CAAA,KAAM;EACpD,MAAMC,YAAY,GAAGL,mBAAmB,CAAC,CAAC;EAC1C,MAAMM,MAAM,GAAGL,eAAe,CAAC,CAAC;EAChC,MAAMM,WAAW,GAAGF,YAAY,CAAC,CAAC;;EAElC,oBACEF,OAAA;IAAKK,SAAS,EAAC,wBAAwB;IAAAC,QAAA,gBACrCN,OAAA;MAAKK,SAAS,EAAE,sBAAsBD,WAAW,GAAG,WAAW,GAAG,cAAc;IAAG;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAC1FV,OAAA,CAACL,GAAG;MAACU,SAAS,EAAE,WAAWD,WAAW,GAAG,kBAAkB,GAAG,cAAc;IAAkC;MAAAG,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eACjHV,OAAA;MAAMK,SAAS,EAAC,mCAAmC;MAAAC,QAAA,EAAC;IAAW;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,EACrEN,WAAW,iBAAIJ,OAAA,CAACJ,KAAK;MAACS,SAAS,EAAC;IAA0B;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAC3D,CAAC;AAEV,CAAC;AAACC,EAAA,GAbWV,sBAAgC;AAAA,IAAAU,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}