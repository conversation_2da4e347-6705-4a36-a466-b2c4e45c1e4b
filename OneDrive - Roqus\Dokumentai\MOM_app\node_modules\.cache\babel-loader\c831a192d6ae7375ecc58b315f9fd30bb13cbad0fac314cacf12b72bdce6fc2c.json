{"ast": null, "code": "import React from'react';import{Zap,Check}from'lucide-react';import{isWhisperConfigured,getConfigStatus}from'../config/whisper';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";export const WhisperStatusIndicator=()=>{const isConfigured=isWhisperConfigured();const status=getConfigStatus();const isConnected=isConfigured;// Assuming isConnected is derived from isConfigured\nreturn/*#__PURE__*/_jsxs(\"div\",{className:\"elegant-whisper-status\",children:[/*#__PURE__*/_jsx(\"div\",{className:`elegant-status-dot ${isConnected?'connected':'disconnected'}`}),/*#__PURE__*/_jsx(Zap,{className:`h-4 w-4 ${isConnected?'text-emerald-300':'text-red-300'} transition-colors duration-300`}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-white/90\",children:\"Whisper A-I\"}),isConnected&&/*#__PURE__*/_jsx(Check,{className:\"h-4 w-4 text-emerald-300\"})]});};", "map": {"version": 3, "names": ["React", "Zap", "Check", "isWhisperConfigured", "getConfigStatus", "jsx", "_jsx", "jsxs", "_jsxs", "WhisperStatusIndicator", "isConfigured", "status", "isConnected", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/components/WhisperStatusIndicator.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { <PERSON>Circle2, XCircle, <PERSON>ert<PERSON><PERSON>gle, Zap, Check } from 'lucide-react';\r\nimport { isWhisperConfigured, getConfigStatus } from '../config/whisper';\r\n\r\nexport const WhisperStatusIndicator: React.FC = () => {\r\n  const isConfigured = isWhisperConfigured();\r\n  const status = getConfigStatus();\r\n  const isConnected = isConfigured; // Assuming isConnected is derived from isConfigured\r\n\r\n  return (\r\n    <div className=\"elegant-whisper-status\">\r\n      <div className={`elegant-status-dot ${isConnected ? 'connected' : 'disconnected'}`}></div>\r\n      <Zap className={`h-4 w-4 ${isConnected ? 'text-emerald-300' : 'text-red-300'} transition-colors duration-300`} />\r\n      <span className=\"text-sm font-medium text-white/90\">Whisper A-I</span>\r\n      {isConnected && <Check className=\"h-4 w-4 text-emerald-300\" />}\r\n    </div>\r\n  );\r\n};"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAA+CC,GAAG,CAAEC,KAAK,KAAQ,cAAc,CAC/E,OAASC,mBAAmB,CAAEC,eAAe,KAAQ,mBAAmB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEzE,MAAO,MAAM,CAAAC,sBAAgC,CAAGA,CAAA,GAAM,CACpD,KAAM,CAAAC,YAAY,CAAGP,mBAAmB,CAAC,CAAC,CAC1C,KAAM,CAAAQ,MAAM,CAAGP,eAAe,CAAC,CAAC,CAChC,KAAM,CAAAQ,WAAW,CAAGF,YAAY,CAAE;AAElC,mBACEF,KAAA,QAAKK,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACrCR,IAAA,QAAKO,SAAS,CAAE,sBAAsBD,WAAW,CAAG,WAAW,CAAG,cAAc,EAAG,CAAM,CAAC,cAC1FN,IAAA,CAACL,GAAG,EAACY,SAAS,CAAE,WAAWD,WAAW,CAAG,kBAAkB,CAAG,cAAc,iCAAkC,CAAE,CAAC,cACjHN,IAAA,SAAMO,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,aAAW,CAAM,CAAC,CACrEF,WAAW,eAAIN,IAAA,CAACJ,KAAK,EAACW,SAAS,CAAC,0BAA0B,CAAE,CAAC,EAC3D,CAAC,CAEV,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}