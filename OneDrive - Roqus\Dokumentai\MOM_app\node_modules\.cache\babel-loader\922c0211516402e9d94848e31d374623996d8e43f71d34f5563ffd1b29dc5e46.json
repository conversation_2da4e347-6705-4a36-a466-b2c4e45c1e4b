{"ast": null, "code": "import React,{useState,useCallback}from'react';import{MeetingsList,ErrorBoundary,WhisperStatusIndicator,TranscriptionManager,ProfessionalTranscriptViewer,RecordingPanel,GridControls}from'./components';import{useAudioRecorder,useTranscription}from'./hooks';import{createDemoMeetings}from'./utils/demoData';import{Headphones,Plus,Mic2,TestTube,Zap,List}from'lucide-react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const[meetings,setMeetings]=useState([]);const[currentMeeting,setCurrentMeeting]=useState(null);const[selectedMeetingForTranscript,setSelectedMeetingForTranscript]=useState(null);const[activeView,setActiveView]=useState('recording');// Grid customization state\nconst[gridSize,setGridSize]=useState(120);const[gridRotation,setGridRotation]=useState(0);const[gridColor,setGridColor]=useState('rgba(147, 51, 234, 0.25)');const{recordingState,startRecording,stopRecording,pauseRecording,resumeRecording}=useAudioRecorder();const{transcript,isTranscribing,transcribeAudioEnhanced,cancelTranscription,editSegment,clearTranscript,clearError,currentTranscriptionId,progress,isWhisperConfigured}=useTranscription();const handleStartRecording=useCallback(async title=>{try{await startRecording();const newMeeting={id:Date.now().toString(),title:title,date:new Date(),duration:0,status:'recording',transcriptionStatus:{state:'not_started'}};setCurrentMeeting(newMeeting);setMeetings(prev=>[newMeeting,...prev]);setActiveView('recording');clearTranscript();}catch(error){console.error('Nepavyko pradėti įrašymo:',error);throw error;}},[startRecording,clearTranscript]);const handleStopRecording=useCallback(async()=>{try{const audioBlob=await stopRecording();if(currentMeeting&&audioBlob){const updatedMeeting={...currentMeeting,status:'completed',duration:Math.floor((Date.now()-currentMeeting.date.getTime())/1000),audioBlob,transcriptionStatus:{state:'not_started'}};setCurrentMeeting(updatedMeeting);setMeetings(prev=>prev.map(m=>m.id===currentMeeting.id?updatedMeeting:m));// Switch to transcription management view\nsetActiveView('transcription');}}catch(error){console.error('Nepavyko sustabdyti įrašymo:',error);alert('Nepavyko sustabdyti įrašymo.');}},[stopRecording,currentMeeting]);const handleStartTranscription=useCallback(async meetingId=>{const meeting=meetings.find(m=>m.id===meetingId);if(!meeting||!meeting.audioBlob)return;// Update meeting status to pending\nconst updatedMeeting={...meeting,transcriptionStatus:{state:'pending',startedAt:new Date()}};setMeetings(prev=>prev.map(m=>m.id===meetingId?updatedMeeting:m));setSelectedMeetingForTranscript(updatedMeeting);try{// Start professional transcription\nconst result=await transcribeAudioEnhanced(meeting.audioBlob,meetingId,{onProgress:progress=>{setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:{...m.transcriptionStatus,progress,state:'processing'}}:m));},onStatusUpdate:status=>{setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:status}:m));},enhanceSpeakers:true});// Update meeting with completed transcription\nconst completedMeeting={...updatedMeeting,transcript:result.segments,participants:result.speakers,metadata:result.metadata,transcriptionStatus:{state:'completed',progress:100,startedAt:updatedMeeting.transcriptionStatus.startedAt,completedAt:new Date()}};setMeetings(prev=>prev.map(m=>m.id===meetingId?completedMeeting:m));setSelectedMeetingForTranscript(completedMeeting);// Automatiškai pereiti į rezultatų puslapį\nsetActiveView('transcript');console.log('✅ Transkribavimas sėkmingai baigtas:',{segments:result.segments.length,speakers:result.speakers.length,words:result.metadata.totalWords,confidence:result.metadata.averageConfidence});}catch(error){console.error('❌ Transkribavimo klaida:',error);const errorMeeting={...updatedMeeting,transcriptionStatus:{state:'failed',error:error.message,startedAt:updatedMeeting.transcriptionStatus.startedAt}};setMeetings(prev=>prev.map(m=>m.id===meetingId?errorMeeting:m));}},[meetings,transcribeAudioEnhanced]);const handleCancelTranscription=useCallback(meetingId=>{cancelTranscription();setMeetings(prev=>prev.map(m=>m.id===meetingId?{...m,transcriptionStatus:{...m.transcriptionStatus,state:'cancelled'}}:m));},[cancelTranscription]);const handleEditSegment=useCallback((meetingId,segmentId,newText)=>{editSegment(segmentId,newText);// Update the meeting's transcript\nsetMeetings(prev=>prev.map(meeting=>{var _meeting$transcript;return meeting.id===meetingId?{...meeting,transcript:(_meeting$transcript=meeting.transcript)===null||_meeting$transcript===void 0?void 0:_meeting$transcript.map(segment=>segment.id===segmentId?{...segment,text:newText,isEdited:true,editedAt:new Date(),editedBy:'user'}:segment)}:meeting;}));},[editSegment]);const handleSelectMeeting=useCallback(meeting=>{setCurrentMeeting(meeting);if(meeting.transcript&&meeting.transcript.length>0){setSelectedMeetingForTranscript(meeting);setActiveView('transcript');}},[]);const handleDeleteMeeting=useCallback(meetingId=>{setMeetings(prev=>prev.filter(m=>m.id!==meetingId));if((currentMeeting===null||currentMeeting===void 0?void 0:currentMeeting.id)===meetingId){setCurrentMeeting(null);}if((selectedMeetingForTranscript===null||selectedMeetingForTranscript===void 0?void 0:selectedMeetingForTranscript.id)===meetingId){setSelectedMeetingForTranscript(null);}},[currentMeeting,selectedMeetingForTranscript]);const handleExportMeeting=useCallback(meeting=>{const exportData={title:meeting.title,date:meeting.date.toISOString(),duration:meeting.duration,transcript:meeting.transcript||transcript,participants:meeting.participants||[],metadata:meeting.metadata||{},transcriptionStatus:meeting.transcriptionStatus};const dataStr=JSON.stringify(exportData,null,2);const dataUri='data:application/json;charset=utf-8,'+encodeURIComponent(dataStr);const exportFileDefaultName=`meeting-${meeting.title.replace(/\\s+/g,'-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;const linkElement=document.createElement('a');linkElement.setAttribute('href',dataUri);linkElement.setAttribute('download',exportFileDefaultName);linkElement.click();},[transcript]);const loadDemoData=useCallback(()=>{const demoMeetings=createDemoMeetings().map(meeting=>({...meeting,transcriptionStatus:{state:'completed',progress:100,completedAt:meeting.date}}));setMeetings(demoMeetings);setActiveView('transcript');},[]);return/*#__PURE__*/_jsx(ErrorBoundary,{children:/*#__PURE__*/_jsxs(\"div\",{className:`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${activeView==='recording'?'grid-page-recording':activeView==='transcription'?'grid-page-transcription':activeView==='transcript'?'grid-page-results':'grid-page-home'}`,style:{'--grid-size':`${gridSize}px`,'--grid-rotation':`${gridRotation}deg`,'--grid-color':gridColor},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative z-10\",children:[/*#__PURE__*/_jsx(\"header\",{className:\"fixed top-0 left-0 right-0 z-50\",children:/*#__PURE__*/_jsx(\"div\",{className:\"elegant-navbar-glass border-b border-white/5\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between h-16\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center gap-4\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"elegant-logo-container group\",\"data-tooltip\":\"MOM - Meeting Recording App\",children:/*#__PURE__*/_jsx(Mic2,{className:\"h-5 w-5 text-white group-hover:text-blue-200 transition-colors duration-300\"})}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden sm:block\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",children:\"MOM App\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-white/60 font-medium tracking-wide\",children:\"Meeting Recording & Transcription\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"sm:hidden\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-lg font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\",children:\"MOM\"})})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"elegant-status-container\",children:/*#__PURE__*/_jsx(WhisperStatusIndicator,{})}),/*#__PURE__*/_jsx(\"nav\",{className:\"elegant-nav-container\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"elegant-nav-pills\",children:[/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveView('recording'),className:`elegant-nav-pill ${activeView==='recording'?'active':''}`,\"data-tooltip\":\"\\u012Era\\u0161yti nauj\\u0105 pokalb\\u012F\",children:[/*#__PURE__*/_jsx(Mic2,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden md:inline font-medium\",children:\"\\u012Era\\u0161ymas\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveView('transcription'),className:`elegant-nav-pill ${activeView==='transcription'?'active':''}`,\"data-tooltip\":\"Transkribuoti audio failus\",children:[/*#__PURE__*/_jsx(Zap,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden md:inline font-medium\",children:\"Transkribavimas\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>setActiveView('transcript'),className:`elegant-nav-pill ${activeView==='transcript'?'active':''}`,\"data-tooltip\":\"Per\\u017Ei\\u016Br\\u0117ti rezultatus ir transkriptus\",children:[/*#__PURE__*/_jsx(Headphones,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden md:inline font-medium\",children:\"Rezultatai\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-3\",children:[meetings.length===0&&/*#__PURE__*/_jsxs(\"button\",{onClick:loadDemoData,className:\"elegant-demo-button group\",\"data-tooltip\":\"U\\u017Ekrauti demonstracinius duomenis testavimui\",children:[/*#__PURE__*/_jsx(TestTube,{className:\"h-4 w-4 group-hover:rotate-12 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden lg:inline font-medium\",children:\"Demo\"})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{setActiveView('recording');if(!recordingState.isRecording){handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);}},className:\"elegant-primary-button group\",\"data-tooltip\":\"Prad\\u0117ti nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\",children:[/*#__PURE__*/_jsx(Plus,{className:\"h-4 w-4 group-hover:rotate-90 transition-transform duration-300\"}),/*#__PURE__*/_jsx(\"span\",{className:\"hidden lg:inline font-semibold\",children:\"Naujas pokalbis\"})]})]})]})})})}),/*#__PURE__*/_jsxs(\"main\",{className:\"pt-20 px-4 sm:px-6 lg:px-8 pb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-7xl mx-auto\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 xl:grid-cols-4 gap-6 min-h-[calc(100vh-140px)]\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"xl:col-span-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"relative h-full\",children:[/*#__PURE__*/_jsx(\"div\",{className:`absolute inset-0 transition-all duration-300 ease-out ${activeView==='recording'?'opacity-100 translate-x-0':'opacity-0 -translate-x-4 pointer-events-none'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"card h-full flex flex-col\",style:{padding:'var(--space-6)',background:'var(--color-bg-elevated)',borderRadius:'var(--radius-xl)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between mb-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",style:{background:'linear-gradient(135deg, var(--color-accent-primary), var(--color-accent-hover))'},children:/*#__PURE__*/_jsx(Mic2,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-primary\",children:\"Pokalbio \\u012Fra\\u0161ymas\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-secondary\",children:\"Prad\\u0117kite nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105 arba t\\u0119skite esam\\u0105\"})]})]}),/*#__PURE__*/_jsxs(\"button\",{onClick:()=>{if(!recordingState.isRecording){handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);}},className:\"tooltip btn btn-primary hidden sm:flex\",\"data-tooltip\":\"Prad\\u0117ti nauj\\u0105 pokalbio \\u012Fra\\u0161ym\\u0105\",children:[/*#__PURE__*/_jsx(Plus,{className:\"h-4 w-4\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Naujas pokalbis\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col\",children:/*#__PURE__*/_jsx(RecordingPanel,{recordingState:recordingState,currentMeeting:currentMeeting,onStartRecording:handleStartRecording,onStopRecording:handleStopRecording,onPauseRecording:()=>{},onResumeRecording:()=>{}})})]})}),/*#__PURE__*/_jsx(\"div\",{className:`absolute inset-0 transition-all duration-300 ease-out ${activeView==='transcription'?'opacity-100 translate-x-0':'opacity-0 translate-x-4 pointer-events-none'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"card h-full flex flex-col\",style:{padding:'var(--space-6)',background:'var(--color-bg-elevated)',borderRadius:'var(--radius-xl)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",style:{background:'linear-gradient(135deg, #8b5cf6, #a855f7)'},children:/*#__PURE__*/_jsx(Zap,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-primary\",children:\"Transkribavimas\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-secondary\",children:\"Audio fail\\u0173 konvertavimas \\u012F tekst\\u0105 naudojant AI\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col\",children:/*#__PURE__*/_jsx(TranscriptionManager,{meetings:meetings,onStartTranscription:handleStartTranscription,onCancelTranscription:handleCancelTranscription,isTranscribing:isTranscribing,currentTranscriptionId:currentTranscriptionId,onDeleteMeeting:handleDeleteMeeting,onViewResults:()=>setActiveView('transcript')})})]})}),/*#__PURE__*/_jsx(\"div\",{className:`absolute inset-0 transition-all duration-300 ease-out ${activeView==='transcript'?'opacity-100 translate-x-0':'opacity-0 translate-x-4 pointer-events-none'}`,children:/*#__PURE__*/_jsxs(\"div\",{className:\"card h-full flex flex-col\",style:{padding:'var(--space-6)',background:'var(--color-bg-elevated)',borderRadius:'var(--radius-xl)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\",style:{background:'linear-gradient(135deg, #10b981, #059669)'},children:/*#__PURE__*/_jsx(Headphones,{className:\"h-6 w-6 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold text-primary\",children:\"Rezultatai\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-secondary\",children:\"Per\\u017Ei\\u016Br\\u0117kite ir redaguokite transkribavimo rezultatus\"})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col\",children:/*#__PURE__*/_jsx(ProfessionalTranscriptViewer,{meetings:meetings,onDeleteMeeting:handleDeleteMeeting,onGoToTranscription:()=>setActiveView('transcription')})})]})})]}),\" \"]})}),/*#__PURE__*/_jsx(\"div\",{className:\"xl:col-span-1\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card h-full flex flex-col\",style:{padding:'var(--space-6)',background:'var(--color-bg-elevated)',borderRadius:'var(--radius-xl)'},children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center gap-4 mb-6\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 rounded-lg flex items-center justify-center shadow-md\",style:{background:'linear-gradient(135deg, #6366f1, #8b5cf6)'},children:/*#__PURE__*/_jsx(List,{className:\"h-5 w-5 text-white\"})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-lg font-semibold text-primary\",children:\"Pokalbiai\"}),/*#__PURE__*/_jsxs(\"p\",{className:\"text-xs text-tertiary\",children:[\"Visi j\\u016Bs\\u0173 pokalbiai (\",meetings.length,\")\"]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"flex-1 flex flex-col\",children:/*#__PURE__*/_jsx(MeetingsList,{meetings:meetings,currentMeeting:currentMeeting,onSelectMeeting:handleSelectMeeting,onDeleteMeeting:handleDeleteMeeting,onExportMeeting:()=>{},activeView:\"list\"})})]})})]}),/*#__PURE__*/_jsx(GridControls,{onGridSizeChange:setGridSize,onGridRotationChange:setGridRotation,onGridColorChange:setGridColor,currentSize:gridSize,currentRotation:gridRotation,currentColor:gridColor})]})]}),\" \"]})});}export default App;", "map": {"version": 3, "names": ["React", "useState", "useCallback", "MeetingsList", "Error<PERSON>ou<PERSON><PERSON>", "WhisperStatusIndicator", "TranscriptionManager", "ProfessionalTranscriptViewer", "RecordingPanel", "GridControls", "useAudioRecorder", "useTranscription", "createDemoMeetings", "Headphones", "Plus", "Mic2", "TestTube", "Zap", "List", "jsx", "_jsx", "jsxs", "_jsxs", "App", "meetings", "setMeetings", "currentMeeting", "setCurrentMeeting", "selectedMeetingForTranscript", "setSelectedMeetingForTranscript", "activeView", "setActiveView", "gridSize", "setGridSize", "gridRotation", "setGridRotation", "gridColor", "setGridColor", "recordingState", "startRecording", "stopRecording", "pauseRecording", "resumeRecording", "transcript", "isTranscribing", "transcribeAudioEnhanced", "cancelTranscription", "editSegment", "clearTranscript", "clearError", "currentTranscriptionId", "progress", "isWhisperConfigured", "handleStartRecording", "title", "newMeeting", "id", "Date", "now", "toString", "date", "duration", "status", "transcriptionStatus", "state", "prev", "error", "console", "handleStopRecording", "audioBlob", "updatedMeeting", "Math", "floor", "getTime", "map", "m", "alert", "handleStartTranscription", "meetingId", "meeting", "find", "startedAt", "result", "onProgress", "onStatusUpdate", "enhanceSpeakers", "completedMeeting", "segments", "participants", "speakers", "metadata", "completedAt", "log", "length", "words", "totalWords", "confidence", "averageConfidence", "errorMeeting", "message", "handleCancelTranscription", "handleEditSegment", "segmentId", "newText", "_meeting$transcript", "segment", "text", "isEdited", "editedAt", "editedBy", "handleSelectMeeting", "handleDeleteMeeting", "filter", "handleExportMeeting", "exportData", "toISOString", "dataStr", "JSON", "stringify", "dataUri", "encodeURIComponent", "exportFileDefaultName", "replace", "toLowerCase", "split", "linkElement", "document", "createElement", "setAttribute", "click", "loadDemoData", "demoMeetings", "children", "className", "style", "onClick", "isRecording", "toLocaleString", "padding", "background", "borderRadius", "onStartRecording", "onStopRecording", "onPauseRecording", "onResumeRecording", "onStartTranscription", "onCancelTranscription", "onDeleteMeeting", "onViewResults", "onGoToTranscription", "onSelectMeeting", "onExportMeeting", "onGridSizeChange", "onGridRotationChange", "onGridColorChange", "currentSize", "currentRotation", "currentColor"], "sources": ["C:/Users/<USER>/OneDrive - Roqus/Dokumentai/MOM_app/src/App.tsx"], "sourcesContent": ["import React, { useState, useCallback } from 'react';\nimport { \n  Recording<PERSON>utton, \n  RecordingIndicator, \n  TranscriptViewer, \n  MeetingsList, \n  ErrorBoundary, \n  WhisperConfig,\n  WhisperStatusIndicator,\n  TranscriptionManager,\n  ProfessionalTranscriptViewer,\n  RecordingPanel,\n  CollapsibleTranscriptsList,\n  GridControls\n} from './components';\nimport { useAudioRecorder, useTranscription } from './hooks';\nimport { Meeting, TranscriptionStatus, Speaker } from './types/meeting';\nimport { createDemoMeetings } from './utils/demoData';\nimport { identifySpeakers } from './services/speakerService';\nimport { Headphones, Plus, Mic2, TestTube, Zap, Settings, List } from 'lucide-react';\n\nfunction App() {\n  const [meetings, setMeetings] = useState<Meeting[]>([]);\n  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);\n  const [selectedMeetingForTranscript, setSelectedMeetingForTranscript] = useState<Meeting | null>(null);\n  const [activeView, setActiveView] = useState<'recording' | 'transcription' | 'transcript'>('recording');\n  \n  // Grid customization state\n  const [gridSize, setGridSize] = useState(120);\n  const [gridRotation, setGridRotation] = useState(0);\n  const [gridColor, setGridColor] = useState('rgba(147, 51, 234, 0.25)');\n\n  const { recordingState, startRecording, stopRecording, pauseRecording, resumeRecording } = useAudioRecorder();\n  const { \n    transcript, \n    isTranscribing, \n    transcribeAudioEnhanced, \n    cancelTranscription,\n    editSegment,\n    clearTranscript, \n    clearError,\n    currentTranscriptionId,\n    progress,\n    isWhisperConfigured\n  } = useTranscription();\n\n  const handleStartRecording = useCallback(async (title: string) => {\n    try {\n      await startRecording();\n      \n      const newMeeting: Meeting = {\n        id: Date.now().toString(),\n        title: title,\n        date: new Date(),\n        duration: 0,\n        status: 'recording',\n        transcriptionStatus: {\n          state: 'not_started',\n        },\n      };\n      \n      setCurrentMeeting(newMeeting);\n      setMeetings(prev => [newMeeting, ...prev]);\n      setActiveView('recording');\n      clearTranscript();\n    } catch (error) {\n      console.error('Nepavyko pradėti įrašymo:', error);\n      throw error;\n    }\n  }, [startRecording, clearTranscript]);\n\n  const handleStopRecording = useCallback(async () => {\n    try {\n      const audioBlob = await stopRecording();\n      \n      if (currentMeeting && audioBlob) {\n        const updatedMeeting: Meeting = {\n          ...currentMeeting,\n          status: 'completed',\n          duration: Math.floor((Date.now() - currentMeeting.date.getTime()) / 1000),\n          audioBlob,\n          transcriptionStatus: {\n            state: 'not_started',\n          },\n        };\n\n        setCurrentMeeting(updatedMeeting);\n        setMeetings(prev => \n          prev.map(m => m.id === currentMeeting.id ? updatedMeeting : m)\n        );\n\n        // Switch to transcription management view\n        setActiveView('transcription');\n      }\n    } catch (error) {\n      console.error('Nepavyko sustabdyti įrašymo:', error);\n      alert('Nepavyko sustabdyti įrašymo.');\n    }\n  }, [stopRecording, currentMeeting]);\n\n  const handleStartTranscription = useCallback(async (meetingId: string) => {\n    const meeting = meetings.find(m => m.id === meetingId);\n    if (!meeting || !meeting.audioBlob) return;\n\n    // Update meeting status to pending\n    const updatedMeeting: Meeting = {\n      ...meeting,\n      transcriptionStatus: {\n        state: 'pending',\n        startedAt: new Date(),\n      },\n    };\n\n    setMeetings(prev => prev.map(m => m.id === meetingId ? updatedMeeting : m));\n    setSelectedMeetingForTranscript(updatedMeeting);\n\n    try {\n      // Start professional transcription\n      const result = await transcribeAudioEnhanced(meeting.audioBlob, meetingId, {\n        onProgress: (progress) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: { \n                    ...m.transcriptionStatus, \n                    progress,\n                    state: 'processing' \n                  } \n                }\n              : m\n          ));\n        },\n        onStatusUpdate: (status) => {\n          setMeetings(prev => prev.map(m => \n            m.id === meetingId \n              ? { \n                  ...m, \n                  transcriptionStatus: status \n                }\n              : m\n          ));\n        },\n        enhanceSpeakers: true,\n      });\n\n      // Update meeting with completed transcription\n      const completedMeeting: Meeting = {\n        ...updatedMeeting,\n        transcript: result.segments,\n        participants: result.speakers,\n        metadata: result.metadata,\n        transcriptionStatus: {\n          state: 'completed',\n          progress: 100,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n          completedAt: new Date(),\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? completedMeeting : m));\n      setSelectedMeetingForTranscript(completedMeeting);\n      \n      // Automatiškai pereiti į rezultatų puslapį\n      setActiveView('transcript');\n\n      console.log('✅ Transkribavimas sėkmingai baigtas:', {\n        segments: result.segments.length,\n        speakers: result.speakers.length,\n        words: result.metadata.totalWords,\n        confidence: result.metadata.averageConfidence,\n      });\n\n    } catch (error: any) {\n      console.error('❌ Transkribavimo klaida:', error);\n      \n      const errorMeeting: Meeting = {\n        ...updatedMeeting,\n        transcriptionStatus: {\n          state: 'failed',\n          error: error.message,\n          startedAt: updatedMeeting.transcriptionStatus.startedAt,\n        },\n      };\n\n      setMeetings(prev => prev.map(m => m.id === meetingId ? errorMeeting : m));\n    }\n  }, [meetings, transcribeAudioEnhanced]);\n\n  const handleCancelTranscription = useCallback((meetingId: string) => {\n    cancelTranscription();\n    \n    setMeetings(prev => prev.map(m => \n      m.id === meetingId \n        ? { \n            ...m, \n            transcriptionStatus: { \n              ...m.transcriptionStatus, \n              state: 'cancelled' as const \n            } \n          }\n        : m\n    ));\n  }, [cancelTranscription]);\n\n  const handleEditSegment = useCallback((meetingId: string, segmentId: string, newText: string) => {\n    editSegment(segmentId, newText);\n    \n    // Update the meeting's transcript\n    setMeetings(prev => prev.map(meeting => \n      meeting.id === meetingId\n        ? {\n            ...meeting,\n            transcript: meeting.transcript?.map(segment => \n              segment.id === segmentId \n                ? {\n                    ...segment,\n                    text: newText,\n                    isEdited: true,\n                    editedAt: new Date(),\n                    editedBy: 'user'\n                  }\n                : segment\n            ),\n          }\n        : meeting\n    ));\n  }, [editSegment]);\n\n  const handleSelectMeeting = useCallback((meeting: Meeting) => {\n    setCurrentMeeting(meeting);\n    if (meeting.transcript && meeting.transcript.length > 0) {\n      setSelectedMeetingForTranscript(meeting);\n      setActiveView('transcript');\n    }\n  }, []);\n\n  const handleDeleteMeeting = useCallback((meetingId: string) => {\n    setMeetings(prev => prev.filter(m => m.id !== meetingId));\n    if (currentMeeting?.id === meetingId) {\n      setCurrentMeeting(null);\n    }\n    if (selectedMeetingForTranscript?.id === meetingId) {\n      setSelectedMeetingForTranscript(null);\n    }\n  }, [currentMeeting, selectedMeetingForTranscript]);\n\n  const handleExportMeeting = useCallback((meeting: Meeting) => {\n    const exportData = {\n      title: meeting.title,\n      date: meeting.date.toISOString(),\n      duration: meeting.duration,\n      transcript: meeting.transcript || transcript,\n      participants: meeting.participants || [],\n      metadata: meeting.metadata || {},\n      transcriptionStatus: meeting.transcriptionStatus,\n    };\n\n    const dataStr = JSON.stringify(exportData, null, 2);\n    const dataUri = 'data:application/json;charset=utf-8,'+ encodeURIComponent(dataStr);\n    \n    const exportFileDefaultName = `meeting-${meeting.title.replace(/\\s+/g, '-').toLowerCase()}-${meeting.date.toISOString().split('T')[0]}.json`;\n    \n    const linkElement = document.createElement('a');\n    linkElement.setAttribute('href', dataUri);\n    linkElement.setAttribute('download', exportFileDefaultName);\n    linkElement.click();\n  }, [transcript]);\n\n\n\n  const loadDemoData = useCallback(() => {\n    const demoMeetings = createDemoMeetings().map(meeting => ({\n      ...meeting,\n      transcriptionStatus: {\n        state: 'completed' as const,\n        progress: 100,\n        completedAt: meeting.date,\n      },\n    }));\n    setMeetings(demoMeetings);\n    setActiveView('transcript');\n  }, []);\n\n  return (\n    <ErrorBoundary>\n      <div\n        className={`min-h-screen bg-app-gradient font-inter relative overflow-hidden ${\n          activeView === 'recording' ? 'grid-page-recording' :\n          activeView === 'transcription' ? 'grid-page-transcription' :\n          activeView === 'transcript' ? 'grid-page-results' :\n          'grid-page-home'\n        }`}\n        style={{\n          '--grid-size': `${gridSize}px`,\n          '--grid-rotation': `${gridRotation}deg`,\n          '--grid-color': gridColor,\n        } as React.CSSProperties}\n      >\n        {/* Content wrapper */}\n        <div className=\"relative z-10\">\n          {/* Elegant Modern Header */}\n        <header className=\"fixed top-0 left-0 right-0 z-50\">\n          <div className=\"elegant-navbar-glass border-b border-white/5\">\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n              <div className=\"flex items-center justify-between h-16\">\n\n                {/* Logo and Brand Section */}\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div\n                      className=\"elegant-logo-container group\"\n                      data-tooltip=\"MOM - Meeting Recording App\"\n                    >\n                      <Mic2 className=\"h-5 w-5 text-white group-hover:text-blue-200 transition-colors duration-300\" />\n                    </div>\n                    <div className=\"hidden sm:block\">\n                      <h1 className=\"text-xl font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                        MOM App\n                      </h1>\n                      <p className=\"text-xs text-white/60 font-medium tracking-wide\">\n                        Meeting Recording & Transcription\n                      </p>\n                    </div>\n                    <div className=\"sm:hidden\">\n                      <h1 className=\"text-lg font-bold bg-gradient-to-r from-white to-blue-200 bg-clip-text text-transparent\">\n                        MOM\n                      </h1>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Center Navigation Section */}\n                <div className=\"flex items-center gap-6\">\n                  {/* Whisper Status with enhanced styling */}\n                  <div className=\"elegant-status-container\">\n                    <WhisperStatusIndicator />\n                  </div>\n\n                  {/* Elegant Navigation Pills */}\n                  <nav className=\"elegant-nav-container\">\n                    <div className=\"elegant-nav-pills\">\n                      <button\n                        onClick={() => setActiveView('recording')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'recording' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Įrašyti naują pokalbį\"\n                      >\n                        <Mic2 className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Įrašymas</span>\n                      </button>\n                      <button\n                        onClick={() => setActiveView('transcription')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'transcription' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Transkribuoti audio failus\"\n                      >\n                        <Zap className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Transkribavimas</span>\n                      </button>\n                      <button\n                        onClick={() => setActiveView('transcript')}\n                        className={`elegant-nav-pill ${\n                          activeView === 'transcript' ? 'active' : ''\n                        }`}\n                        data-tooltip=\"Peržiūrėti rezultatus ir transkriptus\"\n                      >\n                        <Headphones className=\"h-4 w-4\" />\n                        <span className=\"hidden md:inline font-medium\">Rezultatai</span>\n                      </button>\n                    </div>\n                  </nav>\n                </div>\n\n                {/* Action Buttons Section */}\n                <div className=\"flex items-center gap-3\">\n                  {meetings.length === 0 && (\n                    <button\n                      onClick={loadDemoData}\n                      className=\"elegant-demo-button group\"\n                      data-tooltip=\"Užkrauti demonstracinius duomenis testavimui\"\n                    >\n                      <TestTube className=\"h-4 w-4 group-hover:rotate-12 transition-transform duration-300\" />\n                      <span className=\"hidden lg:inline font-medium\">Demo</span>\n                    </button>\n                  )}\n\n                  {/* Primary CTA Button */}\n                  <button\n                    onClick={() => {\n                      setActiveView('recording');\n                      if (!recordingState.isRecording) {\n                        handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                      }\n                    }}\n                    className=\"elegant-primary-button group\"\n                    data-tooltip=\"Pradėti naują pokalbio įrašymą\"\n                  >\n                    <Plus className=\"h-4 w-4 group-hover:rotate-90 transition-transform duration-300\" />\n                    <span className=\"hidden lg:inline font-semibold\">Naujas pokalbis</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </header>\n\n\n\n\n\n        {/* Modern Main Content */}\n        <main className=\"pt-20 px-4 sm:px-6 lg:px-8 pb-6\">\n          <div className=\"max-w-7xl mx-auto\">\n            <div className=\"grid grid-cols-1 xl:grid-cols-4 gap-6 min-h-[calc(100vh-140px)]\">\n\n              {/* Main Content Area */}\n              <div className=\"xl:col-span-3\">\n                <div className=\"relative h-full\">\n                  {/* Recording View */}\n                  <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                    activeView === 'recording'\n                      ? 'opacity-100 translate-x-0'\n                      : 'opacity-0 -translate-x-4 pointer-events-none'\n                  }`}>\n                    <div className=\"card h-full flex flex-col\" style={{\n                      padding: 'var(--space-6)',\n                      background: 'var(--color-bg-elevated)',\n                      borderRadius: 'var(--radius-xl)'\n                    }}>\n                      {/* Header */}\n                      <div className=\"flex items-center justify-between mb-6\">\n                        <div className=\"flex items-center gap-4\">\n                          <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                            background: 'linear-gradient(135deg, var(--color-accent-primary), var(--color-accent-hover))'\n                          }}>\n                            <Mic2 className=\"h-6 w-6 text-white\" />\n                          </div>\n                          <div>\n                            <h2 className=\"text-2xl font-semibold text-primary\">Pokalbio įrašymas</h2>\n                            <p className=\"text-sm text-secondary\">Pradėkite naują pokalbio įrašymą arba tęskite esamą</p>\n                          </div>\n                        </div>\n                        <button\n                          onClick={() => {\n                            if (!recordingState.isRecording) {\n                              handleStartRecording(`Pokalbis ${new Date().toLocaleString('lt-LT')}`);\n                            }\n                          }}\n                          className=\"tooltip btn btn-primary hidden sm:flex\"\n                          data-tooltip=\"Pradėti naują pokalbio įrašymą\"\n                        >\n                          <Plus className=\"h-4 w-4\" />\n                          <span>Naujas pokalbis</span>\n                        </button>\n                      </div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 flex flex-col\">\n                        <RecordingPanel\n                          recordingState={recordingState}\n                          currentMeeting={currentMeeting}\n                          onStartRecording={handleStartRecording}\n                          onStopRecording={handleStopRecording}\n                        onPauseRecording={() => {}}\n                        onResumeRecording={() => {}}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcription View */}\n                <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                  activeView === 'transcription'\n                    ? 'opacity-100 translate-x-0'\n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"card h-full flex flex-col\" style={{\n                    padding: 'var(--space-6)',\n                    background: 'var(--color-bg-elevated)',\n                    borderRadius: 'var(--radius-xl)'\n                  }}>\n                    {/* Header */}\n                    <div className=\"flex items-center gap-4 mb-6\">\n                      <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                        background: 'linear-gradient(135deg, #8b5cf6, #a855f7)'\n                      }}>\n                        <Zap className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-2xl font-semibold text-primary\">Transkribavimas</h2>\n                        <p className=\"text-sm text-secondary\">Audio failų konvertavimas į tekstą naudojant AI</p>\n                      </div>\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 flex flex-col\">\n                      <TranscriptionManager\n                        meetings={meetings}\n                        onStartTranscription={handleStartTranscription}\n                        onCancelTranscription={handleCancelTranscription}\n                        isTranscribing={isTranscribing}\n                        currentTranscriptionId={currentTranscriptionId}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onViewResults={() => setActiveView('transcript')}\n                      />\n                    </div>\n                  </div>\n                </div>\n\n                {/* Transcript View */}\n                <div className={`absolute inset-0 transition-all duration-300 ease-out ${\n                  activeView === 'transcript'\n                    ? 'opacity-100 translate-x-0'\n                    : 'opacity-0 translate-x-4 pointer-events-none'\n                }`}>\n                  <div className=\"card h-full flex flex-col\" style={{\n                    padding: 'var(--space-6)',\n                    background: 'var(--color-bg-elevated)',\n                    borderRadius: 'var(--radius-xl)'\n                  }}>\n                    {/* Header */}\n                    <div className=\"flex items-center gap-4 mb-6\">\n                      <div className=\"w-12 h-12 rounded-xl flex items-center justify-center shadow-lg\" style={{\n                        background: 'linear-gradient(135deg, #10b981, #059669)'\n                      }}>\n                        <Headphones className=\"h-6 w-6 text-white\" />\n                      </div>\n                      <div>\n                        <h2 className=\"text-2xl font-semibold text-primary\">Rezultatai</h2>\n                        <p className=\"text-sm text-secondary\">Peržiūrėkite ir redaguokite transkribavimo rezultatus</p>\n                      </div>\n                    </div>\n\n                    {/* Content */}\n                    <div className=\"flex-1 flex flex-col\">\n                      <ProfessionalTranscriptViewer\n                        meetings={meetings}\n                        onDeleteMeeting={handleDeleteMeeting}\n                        onGoToTranscription={() => setActiveView('transcription')}\n                      />\n                    </div>\n                  </div>\n                </div>\n                </div> {/* Close relative h-full div */}\n              </div>\n            </div>\n\n            {/* Sidebar - Meetings List */}\n            <div className=\"xl:col-span-1\">\n              <div className=\"card h-full flex flex-col\" style={{\n                padding: 'var(--space-6)',\n                background: 'var(--color-bg-elevated)',\n                borderRadius: 'var(--radius-xl)'\n              }}>\n                {/* Sidebar Header */}\n                <div className=\"flex items-center gap-4 mb-6\">\n                  <div className=\"w-10 h-10 rounded-lg flex items-center justify-center shadow-md\" style={{\n                    background: 'linear-gradient(135deg, #6366f1, #8b5cf6)'\n                  }}>\n                    <List className=\"h-5 w-5 text-white\" />\n                  </div>\n                  <div>\n                    <h2 className=\"text-lg font-semibold text-primary\">Pokalbiai</h2>\n                    <p className=\"text-xs text-tertiary\">Visi jūsų pokalbiai ({meetings.length})</p>\n                  </div>\n                </div>\n\n                {/* Sidebar Content */}\n                <div className=\"flex-1 flex flex-col\">\n                  <MeetingsList\n                    meetings={meetings}\n                    currentMeeting={currentMeeting}\n                    onSelectMeeting={handleSelectMeeting}\n                    onDeleteMeeting={handleDeleteMeeting}\n                    onExportMeeting={() => {}}\n                    activeView=\"list\"\n                  />\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Grid Controls */}\n          <GridControls\n            onGridSizeChange={setGridSize}\n            onGridRotationChange={setGridRotation}\n            onGridColorChange={setGridColor}\n            currentSize={gridSize}\n            currentRotation={gridRotation}\n            currentColor={gridColor}\n          />\n        </main>\n        </div> {/* Close content wrapper div */}\n      </div>\n    </ErrorBoundary>\n  );\n}\n\nexport default App; "], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,WAAW,KAAQ,OAAO,CACpD,OAIEC,YAAY,CACZC,aAAa,CAEbC,sBAAsB,CACtBC,oBAAoB,CACpBC,4BAA4B,CAC5BC,cAAc,CAEdC,YAAY,KACP,cAAc,CACrB,OAASC,gBAAgB,CAAEC,gBAAgB,KAAQ,SAAS,CAE5D,OAASC,kBAAkB,KAAQ,kBAAkB,CAErD,OAASC,UAAU,CAAEC,IAAI,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,GAAG,CAAYC,IAAI,KAAQ,cAAc,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAErF,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAY,EAAE,CAAC,CACvD,KAAM,CAACyB,cAAc,CAAEC,iBAAiB,CAAC,CAAG1B,QAAQ,CAAiB,IAAI,CAAC,CAC1E,KAAM,CAAC2B,4BAA4B,CAAEC,+BAA+B,CAAC,CAAG5B,QAAQ,CAAiB,IAAI,CAAC,CACtG,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAA+C,WAAW,CAAC,CAEvG;AACA,KAAM,CAAC+B,QAAQ,CAAEC,WAAW,CAAC,CAAGhC,QAAQ,CAAC,GAAG,CAAC,CAC7C,KAAM,CAACiC,YAAY,CAAEC,eAAe,CAAC,CAAGlC,QAAQ,CAAC,CAAC,CAAC,CACnD,KAAM,CAACmC,SAAS,CAAEC,YAAY,CAAC,CAAGpC,QAAQ,CAAC,0BAA0B,CAAC,CAEtE,KAAM,CAAEqC,cAAc,CAAEC,cAAc,CAAEC,aAAa,CAAEC,cAAc,CAAEC,eAAgB,CAAC,CAAGhC,gBAAgB,CAAC,CAAC,CAC7G,KAAM,CACJiC,UAAU,CACVC,cAAc,CACdC,uBAAuB,CACvBC,mBAAmB,CACnBC,WAAW,CACXC,eAAe,CACfC,UAAU,CACVC,sBAAsB,CACtBC,QAAQ,CACRC,mBACF,CAAC,CAAGzC,gBAAgB,CAAC,CAAC,CAEtB,KAAM,CAAA0C,oBAAoB,CAAGnD,WAAW,CAAC,KAAO,CAAAoD,KAAa,EAAK,CAChE,GAAI,CACF,KAAM,CAAAf,cAAc,CAAC,CAAC,CAEtB,KAAM,CAAAgB,UAAmB,CAAG,CAC1BC,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBL,KAAK,CAAEA,KAAK,CACZM,IAAI,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAChBI,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,WAAW,CACnBC,mBAAmB,CAAE,CACnBC,KAAK,CAAE,aACT,CACF,CAAC,CAEDrC,iBAAiB,CAAC4B,UAAU,CAAC,CAC7B9B,WAAW,CAACwC,IAAI,EAAI,CAACV,UAAU,CAAE,GAAGU,IAAI,CAAC,CAAC,CAC1ClC,aAAa,CAAC,WAAW,CAAC,CAC1BiB,eAAe,CAAC,CAAC,CACnB,CAAE,MAAOkB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,CAAEA,KAAK,CAAC,CACjD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAAE,CAAC3B,cAAc,CAAES,eAAe,CAAC,CAAC,CAErC,KAAM,CAAAoB,mBAAmB,CAAGlE,WAAW,CAAC,SAAY,CAClD,GAAI,CACF,KAAM,CAAAmE,SAAS,CAAG,KAAM,CAAA7B,aAAa,CAAC,CAAC,CAEvC,GAAId,cAAc,EAAI2C,SAAS,CAAE,CAC/B,KAAM,CAAAC,cAAuB,CAAG,CAC9B,GAAG5C,cAAc,CACjBoC,MAAM,CAAE,WAAW,CACnBD,QAAQ,CAAEU,IAAI,CAACC,KAAK,CAAC,CAACf,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGhC,cAAc,CAACkC,IAAI,CAACa,OAAO,CAAC,CAAC,EAAI,IAAI,CAAC,CACzEJ,SAAS,CACTN,mBAAmB,CAAE,CACnBC,KAAK,CAAE,aACT,CACF,CAAC,CAEDrC,iBAAiB,CAAC2C,cAAc,CAAC,CACjC7C,WAAW,CAACwC,IAAI,EACdA,IAAI,CAACS,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAK9B,cAAc,CAAC8B,EAAE,CAAGc,cAAc,CAAGK,CAAC,CAC/D,CAAC,CAED;AACA5C,aAAa,CAAC,eAAe,CAAC,CAChC,CACF,CAAE,MAAOmC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,8BAA8B,CAAEA,KAAK,CAAC,CACpDU,KAAK,CAAC,8BAA8B,CAAC,CACvC,CACF,CAAC,CAAE,CAACpC,aAAa,CAAEd,cAAc,CAAC,CAAC,CAEnC,KAAM,CAAAmD,wBAAwB,CAAG3E,WAAW,CAAC,KAAO,CAAA4E,SAAiB,EAAK,CACxE,KAAM,CAAAC,OAAO,CAAGvD,QAAQ,CAACwD,IAAI,CAACL,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CAAC,CACtD,GAAI,CAACC,OAAO,EAAI,CAACA,OAAO,CAACV,SAAS,CAAE,OAEpC;AACA,KAAM,CAAAC,cAAuB,CAAG,CAC9B,GAAGS,OAAO,CACVhB,mBAAmB,CAAE,CACnBC,KAAK,CAAE,SAAS,CAChBiB,SAAS,CAAE,GAAI,CAAAxB,IAAI,CAAC,CACtB,CACF,CAAC,CAEDhC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CAAGR,cAAc,CAAGK,CAAC,CAAC,CAAC,CAC3E9C,+BAA+B,CAACyC,cAAc,CAAC,CAE/C,GAAI,CACF;AACA,KAAM,CAAAY,MAAM,CAAG,KAAM,CAAArC,uBAAuB,CAACkC,OAAO,CAACV,SAAS,CAAES,SAAS,CAAE,CACzEK,UAAU,CAAGhC,QAAQ,EAAK,CACxB1B,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CACd,CACE,GAAGH,CAAC,CACJZ,mBAAmB,CAAE,CACnB,GAAGY,CAAC,CAACZ,mBAAmB,CACxBZ,QAAQ,CACRa,KAAK,CAAE,YACT,CACF,CAAC,CACDW,CACN,CAAC,CAAC,CACJ,CAAC,CACDS,cAAc,CAAGtB,MAAM,EAAK,CAC1BrC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CACd,CACE,GAAGH,CAAC,CACJZ,mBAAmB,CAAED,MACvB,CAAC,CACDa,CACN,CAAC,CAAC,CACJ,CAAC,CACDU,eAAe,CAAE,IACnB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,gBAAyB,CAAG,CAChC,GAAGhB,cAAc,CACjB3B,UAAU,CAAEuC,MAAM,CAACK,QAAQ,CAC3BC,YAAY,CAAEN,MAAM,CAACO,QAAQ,CAC7BC,QAAQ,CAAER,MAAM,CAACQ,QAAQ,CACzB3B,mBAAmB,CAAE,CACnBC,KAAK,CAAE,WAAW,CAClBb,QAAQ,CAAE,GAAG,CACb8B,SAAS,CAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAAS,CACvDU,WAAW,CAAE,GAAI,CAAAlC,IAAI,CAAC,CACxB,CACF,CAAC,CAEDhC,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CAAGQ,gBAAgB,CAAGX,CAAC,CAAC,CAAC,CAC7E9C,+BAA+B,CAACyD,gBAAgB,CAAC,CAEjD;AACAvD,aAAa,CAAC,YAAY,CAAC,CAE3BoC,OAAO,CAACyB,GAAG,CAAC,sCAAsC,CAAE,CAClDL,QAAQ,CAAEL,MAAM,CAACK,QAAQ,CAACM,MAAM,CAChCJ,QAAQ,CAAEP,MAAM,CAACO,QAAQ,CAACI,MAAM,CAChCC,KAAK,CAAEZ,MAAM,CAACQ,QAAQ,CAACK,UAAU,CACjCC,UAAU,CAAEd,MAAM,CAACQ,QAAQ,CAACO,iBAC9B,CAAC,CAAC,CAEJ,CAAE,MAAO/B,KAAU,CAAE,CACnBC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAEhD,KAAM,CAAAgC,YAAqB,CAAG,CAC5B,GAAG5B,cAAc,CACjBP,mBAAmB,CAAE,CACnBC,KAAK,CAAE,QAAQ,CACfE,KAAK,CAAEA,KAAK,CAACiC,OAAO,CACpBlB,SAAS,CAAEX,cAAc,CAACP,mBAAmB,CAACkB,SAChD,CACF,CAAC,CAEDxD,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CAAGoB,YAAY,CAAGvB,CAAC,CAAC,CAAC,CAC3E,CACF,CAAC,CAAE,CAACnD,QAAQ,CAAEqB,uBAAuB,CAAC,CAAC,CAEvC,KAAM,CAAAuD,yBAAyB,CAAGlG,WAAW,CAAE4E,SAAiB,EAAK,CACnEhC,mBAAmB,CAAC,CAAC,CAErBrB,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACC,CAAC,EAC5BA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CACd,CACE,GAAGH,CAAC,CACJZ,mBAAmB,CAAE,CACnB,GAAGY,CAAC,CAACZ,mBAAmB,CACxBC,KAAK,CAAE,WACT,CACF,CAAC,CACDW,CACN,CAAC,CAAC,CACJ,CAAC,CAAE,CAAC7B,mBAAmB,CAAC,CAAC,CAEzB,KAAM,CAAAuD,iBAAiB,CAAGnG,WAAW,CAAC,CAAC4E,SAAiB,CAAEwB,SAAiB,CAAEC,OAAe,GAAK,CAC/FxD,WAAW,CAACuD,SAAS,CAAEC,OAAO,CAAC,CAE/B;AACA9E,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAACS,GAAG,CAACK,OAAO,OAAAyB,mBAAA,OAClC,CAAAzB,OAAO,CAACvB,EAAE,GAAKsB,SAAS,CACpB,CACE,GAAGC,OAAO,CACVpC,UAAU,EAAA6D,mBAAA,CAAEzB,OAAO,CAACpC,UAAU,UAAA6D,mBAAA,iBAAlBA,mBAAA,CAAoB9B,GAAG,CAAC+B,OAAO,EACzCA,OAAO,CAACjD,EAAE,GAAK8C,SAAS,CACpB,CACE,GAAGG,OAAO,CACVC,IAAI,CAAEH,OAAO,CACbI,QAAQ,CAAE,IAAI,CACdC,QAAQ,CAAE,GAAI,CAAAnD,IAAI,CAAC,CAAC,CACpBoD,QAAQ,CAAE,MACZ,CAAC,CACDJ,OACN,CACF,CAAC,CACD1B,OAAO,EACb,CAAC,CAAC,CACJ,CAAC,CAAE,CAAChC,WAAW,CAAC,CAAC,CAEjB,KAAM,CAAA+D,mBAAmB,CAAG5G,WAAW,CAAE6E,OAAgB,EAAK,CAC5DpD,iBAAiB,CAACoD,OAAO,CAAC,CAC1B,GAAIA,OAAO,CAACpC,UAAU,EAAIoC,OAAO,CAACpC,UAAU,CAACkD,MAAM,CAAG,CAAC,CAAE,CACvDhE,+BAA+B,CAACkD,OAAO,CAAC,CACxChD,aAAa,CAAC,YAAY,CAAC,CAC7B,CACF,CAAC,CAAE,EAAE,CAAC,CAEN,KAAM,CAAAgF,mBAAmB,CAAG7G,WAAW,CAAE4E,SAAiB,EAAK,CAC7DrD,WAAW,CAACwC,IAAI,EAAIA,IAAI,CAAC+C,MAAM,CAACrC,CAAC,EAAIA,CAAC,CAACnB,EAAE,GAAKsB,SAAS,CAAC,CAAC,CACzD,GAAI,CAAApD,cAAc,SAAdA,cAAc,iBAAdA,cAAc,CAAE8B,EAAE,IAAKsB,SAAS,CAAE,CACpCnD,iBAAiB,CAAC,IAAI,CAAC,CACzB,CACA,GAAI,CAAAC,4BAA4B,SAA5BA,4BAA4B,iBAA5BA,4BAA4B,CAAE4B,EAAE,IAAKsB,SAAS,CAAE,CAClDjD,+BAA+B,CAAC,IAAI,CAAC,CACvC,CACF,CAAC,CAAE,CAACH,cAAc,CAAEE,4BAA4B,CAAC,CAAC,CAElD,KAAM,CAAAqF,mBAAmB,CAAG/G,WAAW,CAAE6E,OAAgB,EAAK,CAC5D,KAAM,CAAAmC,UAAU,CAAG,CACjB5D,KAAK,CAAEyB,OAAO,CAACzB,KAAK,CACpBM,IAAI,CAAEmB,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAChCtD,QAAQ,CAAEkB,OAAO,CAAClB,QAAQ,CAC1BlB,UAAU,CAAEoC,OAAO,CAACpC,UAAU,EAAIA,UAAU,CAC5C6C,YAAY,CAAET,OAAO,CAACS,YAAY,EAAI,EAAE,CACxCE,QAAQ,CAAEX,OAAO,CAACW,QAAQ,EAAI,CAAC,CAAC,CAChC3B,mBAAmB,CAAEgB,OAAO,CAAChB,mBAC/B,CAAC,CAED,KAAM,CAAAqD,OAAO,CAAGC,IAAI,CAACC,SAAS,CAACJ,UAAU,CAAE,IAAI,CAAE,CAAC,CAAC,CACnD,KAAM,CAAAK,OAAO,CAAG,sCAAsC,CAAEC,kBAAkB,CAACJ,OAAO,CAAC,CAEnF,KAAM,CAAAK,qBAAqB,CAAG,WAAW1C,OAAO,CAACzB,KAAK,CAACoE,OAAO,CAAC,MAAM,CAAE,GAAG,CAAC,CAACC,WAAW,CAAC,CAAC,IAAI5C,OAAO,CAACnB,IAAI,CAACuD,WAAW,CAAC,CAAC,CAACS,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAE5I,KAAM,CAAAC,WAAW,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CAC/CF,WAAW,CAACG,YAAY,CAAC,MAAM,CAAET,OAAO,CAAC,CACzCM,WAAW,CAACG,YAAY,CAAC,UAAU,CAAEP,qBAAqB,CAAC,CAC3DI,WAAW,CAACI,KAAK,CAAC,CAAC,CACrB,CAAC,CAAE,CAACtF,UAAU,CAAC,CAAC,CAIhB,KAAM,CAAAuF,YAAY,CAAGhI,WAAW,CAAC,IAAM,CACrC,KAAM,CAAAiI,YAAY,CAAGvH,kBAAkB,CAAC,CAAC,CAAC8D,GAAG,CAACK,OAAO,GAAK,CACxD,GAAGA,OAAO,CACVhB,mBAAmB,CAAE,CACnBC,KAAK,CAAE,WAAoB,CAC3Bb,QAAQ,CAAE,GAAG,CACbwC,WAAW,CAAEZ,OAAO,CAACnB,IACvB,CACF,CAAC,CAAC,CAAC,CACHnC,WAAW,CAAC0G,YAAY,CAAC,CACzBpG,aAAa,CAAC,YAAY,CAAC,CAC7B,CAAC,CAAE,EAAE,CAAC,CAEN,mBACEX,IAAA,CAAChB,aAAa,EAAAgI,QAAA,cACZ9G,KAAA,QACE+G,SAAS,CAAE,oEACTvG,UAAU,GAAK,WAAW,CAAG,qBAAqB,CAClDA,UAAU,GAAK,eAAe,CAAG,yBAAyB,CAC1DA,UAAU,GAAK,YAAY,CAAG,mBAAmB,CACjD,gBAAgB,EACf,CACHwG,KAAK,CAAE,CACL,aAAa,CAAE,GAAGtG,QAAQ,IAAI,CAC9B,iBAAiB,CAAE,GAAGE,YAAY,KAAK,CACvC,cAAc,CAAEE,SAClB,CAAyB,CAAAgG,QAAA,eAGzB9G,KAAA,QAAK+G,SAAS,CAAC,eAAe,CAAAD,QAAA,eAE9BhH,IAAA,WAAQiH,SAAS,CAAC,iCAAiC,CAAAD,QAAA,cACjDhH,IAAA,QAAKiH,SAAS,CAAC,8CAA8C,CAAAD,QAAA,cAC3DhH,IAAA,QAAKiH,SAAS,CAAC,wCAAwC,CAAAD,QAAA,cACrD9G,KAAA,QAAK+G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eAGrDhH,IAAA,QAAKiH,SAAS,CAAC,yBAAyB,CAAAD,QAAA,cACtC9G,KAAA,QAAK+G,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACtChH,IAAA,QACEiH,SAAS,CAAC,8BAA8B,CACxC,eAAa,6BAA6B,CAAAD,QAAA,cAE1ChH,IAAA,CAACL,IAAI,EAACsH,SAAS,CAAC,6EAA6E,CAAE,CAAC,CAC7F,CAAC,cACN/G,KAAA,QAAK+G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAC9BhH,IAAA,OAAIiH,SAAS,CAAC,yFAAyF,CAAAD,QAAA,CAAC,SAExG,CAAI,CAAC,cACLhH,IAAA,MAAGiH,SAAS,CAAC,iDAAiD,CAAAD,QAAA,CAAC,mCAE/D,CAAG,CAAC,EACD,CAAC,cACNhH,IAAA,QAAKiH,SAAS,CAAC,WAAW,CAAAD,QAAA,cACxBhH,IAAA,OAAIiH,SAAS,CAAC,yFAAyF,CAAAD,QAAA,CAAC,KAExG,CAAI,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,cAGN9G,KAAA,QAAK+G,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eAEtChH,IAAA,QAAKiH,SAAS,CAAC,0BAA0B,CAAAD,QAAA,cACvChH,IAAA,CAACf,sBAAsB,GAAE,CAAC,CACvB,CAAC,cAGNe,IAAA,QAAKiH,SAAS,CAAC,uBAAuB,CAAAD,QAAA,cACpC9G,KAAA,QAAK+G,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChC9G,KAAA,WACEiH,OAAO,CAAEA,CAAA,GAAMxG,aAAa,CAAC,WAAW,CAAE,CAC1CsG,SAAS,CAAE,oBACTvG,UAAU,GAAK,WAAW,CAAG,QAAQ,CAAG,EAAE,EACzC,CACH,eAAa,2CAAuB,CAAAsG,QAAA,eAEpChH,IAAA,CAACL,IAAI,EAACsH,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BjH,IAAA,SAAMiH,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,oBAAQ,CAAM,CAAC,EACxD,CAAC,cACT9G,KAAA,WACEiH,OAAO,CAAEA,CAAA,GAAMxG,aAAa,CAAC,eAAe,CAAE,CAC9CsG,SAAS,CAAE,oBACTvG,UAAU,GAAK,eAAe,CAAG,QAAQ,CAAG,EAAE,EAC7C,CACH,eAAa,4BAA4B,CAAAsG,QAAA,eAEzChH,IAAA,CAACH,GAAG,EAACoH,SAAS,CAAC,SAAS,CAAE,CAAC,cAC3BjH,IAAA,SAAMiH,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,iBAAe,CAAM,CAAC,EAC/D,CAAC,cACT9G,KAAA,WACEiH,OAAO,CAAEA,CAAA,GAAMxG,aAAa,CAAC,YAAY,CAAE,CAC3CsG,SAAS,CAAE,oBACTvG,UAAU,GAAK,YAAY,CAAG,QAAQ,CAAG,EAAE,EAC1C,CACH,eAAa,sDAAuC,CAAAsG,QAAA,eAEpDhH,IAAA,CAACP,UAAU,EAACwH,SAAS,CAAC,SAAS,CAAE,CAAC,cAClCjH,IAAA,SAAMiH,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,YAAU,CAAM,CAAC,EAC1D,CAAC,EACN,CAAC,CACH,CAAC,EACH,CAAC,cAGN9G,KAAA,QAAK+G,SAAS,CAAC,yBAAyB,CAAAD,QAAA,EACrC5G,QAAQ,CAACqE,MAAM,GAAK,CAAC,eACpBvE,KAAA,WACEiH,OAAO,CAAEL,YAAa,CACtBG,SAAS,CAAC,2BAA2B,CACrC,eAAa,mDAA8C,CAAAD,QAAA,eAE3DhH,IAAA,CAACJ,QAAQ,EAACqH,SAAS,CAAC,iEAAiE,CAAE,CAAC,cACxFjH,IAAA,SAAMiH,SAAS,CAAC,8BAA8B,CAAAD,QAAA,CAAC,MAAI,CAAM,CAAC,EACpD,CACT,cAGD9G,KAAA,WACEiH,OAAO,CAAEA,CAAA,GAAM,CACbxG,aAAa,CAAC,WAAW,CAAC,CAC1B,GAAI,CAACO,cAAc,CAACkG,WAAW,CAAE,CAC/BnF,oBAAoB,CAAC,YAAY,GAAI,CAAAI,IAAI,CAAC,CAAC,CAACgF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CACxE,CACF,CAAE,CACFJ,SAAS,CAAC,8BAA8B,CACxC,eAAa,yDAAgC,CAAAD,QAAA,eAE7ChH,IAAA,CAACN,IAAI,EAACuH,SAAS,CAAC,iEAAiE,CAAE,CAAC,cACpFjH,IAAA,SAAMiH,SAAS,CAAC,gCAAgC,CAAAD,QAAA,CAAC,iBAAe,CAAM,CAAC,EACjE,CAAC,EACN,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CACA,CAAC,cAOT9G,KAAA,SAAM+G,SAAS,CAAC,iCAAiC,CAAAD,QAAA,eAC/C9G,KAAA,QAAK+G,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eAChChH,IAAA,QAAKiH,SAAS,CAAC,iEAAiE,CAAAD,QAAA,cAG9E9G,KAAA,QAAK+G,SAAS,CAAC,eAAe,CAAAD,QAAA,eAC5B9G,KAAA,QAAK+G,SAAS,CAAC,iBAAiB,CAAAD,QAAA,eAE9BhH,IAAA,QAAKiH,SAAS,CAAE,yDACdvG,UAAU,GAAK,WAAW,CACtB,2BAA2B,CAC3B,8CAA8C,EACjD,CAAAsG,QAAA,cACD9G,KAAA,QAAK+G,SAAS,CAAC,2BAA2B,CAACC,KAAK,CAAE,CAChDI,OAAO,CAAE,gBAAgB,CACzBC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,kBAChB,CAAE,CAAAR,QAAA,eAEA9G,KAAA,QAAK+G,SAAS,CAAC,wCAAwC,CAAAD,QAAA,eACrD9G,KAAA,QAAK+G,SAAS,CAAC,yBAAyB,CAAAD,QAAA,eACtChH,IAAA,QAAKiH,SAAS,CAAC,iEAAiE,CAACC,KAAK,CAAE,CACtFK,UAAU,CAAE,iFACd,CAAE,CAAAP,QAAA,cACAhH,IAAA,CAACL,IAAI,EAACsH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,cACN/G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,OAAIiH,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,6BAAiB,CAAI,CAAC,cAC1EhH,IAAA,MAAGiH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,wFAAmD,CAAG,CAAC,EAC1F,CAAC,EACH,CAAC,cACN9G,KAAA,WACEiH,OAAO,CAAEA,CAAA,GAAM,CACb,GAAI,CAACjG,cAAc,CAACkG,WAAW,CAAE,CAC/BnF,oBAAoB,CAAC,YAAY,GAAI,CAAAI,IAAI,CAAC,CAAC,CAACgF,cAAc,CAAC,OAAO,CAAC,EAAE,CAAC,CACxE,CACF,CAAE,CACFJ,SAAS,CAAC,wCAAwC,CAClD,eAAa,yDAAgC,CAAAD,QAAA,eAE7ChH,IAAA,CAACN,IAAI,EAACuH,SAAS,CAAC,SAAS,CAAE,CAAC,cAC5BjH,IAAA,SAAAgH,QAAA,CAAM,iBAAe,CAAM,CAAC,EACtB,CAAC,EACN,CAAC,cAGNhH,IAAA,QAAKiH,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChH,IAAA,CAACZ,cAAc,EACb8B,cAAc,CAAEA,cAAe,CAC/BZ,cAAc,CAAEA,cAAe,CAC/BmH,gBAAgB,CAAExF,oBAAqB,CACvCyF,eAAe,CAAE1E,mBAAoB,CACvC2E,gBAAgB,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC3BC,iBAAiB,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC7B,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGN5H,IAAA,QAAKiH,SAAS,CAAE,yDACdvG,UAAU,GAAK,eAAe,CAC1B,2BAA2B,CAC3B,6CAA6C,EAChD,CAAAsG,QAAA,cACD9G,KAAA,QAAK+G,SAAS,CAAC,2BAA2B,CAACC,KAAK,CAAE,CAChDI,OAAO,CAAE,gBAAgB,CACzBC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,kBAChB,CAAE,CAAAR,QAAA,eAEA9G,KAAA,QAAK+G,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3ChH,IAAA,QAAKiH,SAAS,CAAC,iEAAiE,CAACC,KAAK,CAAE,CACtFK,UAAU,CAAE,2CACd,CAAE,CAAAP,QAAA,cACAhH,IAAA,CAACH,GAAG,EAACoH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACnC,CAAC,cACN/G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,OAAIiH,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,iBAAe,CAAI,CAAC,cACxEhH,IAAA,MAAGiH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,gEAA+C,CAAG,CAAC,EACtF,CAAC,EACH,CAAC,cAGNhH,IAAA,QAAKiH,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChH,IAAA,CAACd,oBAAoB,EACnBkB,QAAQ,CAAEA,QAAS,CACnByH,oBAAoB,CAAEpE,wBAAyB,CAC/CqE,qBAAqB,CAAE9C,yBAA0B,CACjDxD,cAAc,CAAEA,cAAe,CAC/BM,sBAAsB,CAAEA,sBAAuB,CAC/CiG,eAAe,CAAEpC,mBAAoB,CACrCqC,aAAa,CAAEA,CAAA,GAAMrH,aAAa,CAAC,YAAY,CAAE,CAClD,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,cAGNX,IAAA,QAAKiH,SAAS,CAAE,yDACdvG,UAAU,GAAK,YAAY,CACvB,2BAA2B,CAC3B,6CAA6C,EAChD,CAAAsG,QAAA,cACD9G,KAAA,QAAK+G,SAAS,CAAC,2BAA2B,CAACC,KAAK,CAAE,CAChDI,OAAO,CAAE,gBAAgB,CACzBC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,kBAChB,CAAE,CAAAR,QAAA,eAEA9G,KAAA,QAAK+G,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3ChH,IAAA,QAAKiH,SAAS,CAAC,iEAAiE,CAACC,KAAK,CAAE,CACtFK,UAAU,CAAE,2CACd,CAAE,CAAAP,QAAA,cACAhH,IAAA,CAACP,UAAU,EAACwH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CAC1C,CAAC,cACN/G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,OAAIiH,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,YAAU,CAAI,CAAC,cACnEhH,IAAA,MAAGiH,SAAS,CAAC,wBAAwB,CAAAD,QAAA,CAAC,sEAAqD,CAAG,CAAC,EAC5F,CAAC,EACH,CAAC,cAGNhH,IAAA,QAAKiH,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChH,IAAA,CAACb,4BAA4B,EAC3BiB,QAAQ,CAAEA,QAAS,CACnB2H,eAAe,CAAEpC,mBAAoB,CACrCsC,mBAAmB,CAAEA,CAAA,GAAMtH,aAAa,CAAC,eAAe,CAAE,CAC3D,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACD,CAAC,IAAC,EACJ,CAAC,CACH,CAAC,cAGNX,IAAA,QAAKiH,SAAS,CAAC,eAAe,CAAAD,QAAA,cAC5B9G,KAAA,QAAK+G,SAAS,CAAC,2BAA2B,CAACC,KAAK,CAAE,CAChDI,OAAO,CAAE,gBAAgB,CACzBC,UAAU,CAAE,0BAA0B,CACtCC,YAAY,CAAE,kBAChB,CAAE,CAAAR,QAAA,eAEA9G,KAAA,QAAK+G,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC3ChH,IAAA,QAAKiH,SAAS,CAAC,iEAAiE,CAACC,KAAK,CAAE,CACtFK,UAAU,CAAE,2CACd,CAAE,CAAAP,QAAA,cACAhH,IAAA,CAACF,IAAI,EAACmH,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,cACN/G,KAAA,QAAA8G,QAAA,eACEhH,IAAA,OAAIiH,SAAS,CAAC,oCAAoC,CAAAD,QAAA,CAAC,WAAS,CAAI,CAAC,cACjE9G,KAAA,MAAG+G,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAC,iCAAqB,CAAC5G,QAAQ,CAACqE,MAAM,CAAC,GAAC,EAAG,CAAC,EAC7E,CAAC,EACH,CAAC,cAGNzE,IAAA,QAAKiH,SAAS,CAAC,sBAAsB,CAAAD,QAAA,cACnChH,IAAA,CAACjB,YAAY,EACXqB,QAAQ,CAAEA,QAAS,CACnBE,cAAc,CAAEA,cAAe,CAC/B4H,eAAe,CAAExC,mBAAoB,CACrCqC,eAAe,CAAEpC,mBAAoB,CACrCwC,eAAe,CAAEA,CAAA,GAAM,CAAC,CAAE,CAC1BzH,UAAU,CAAC,MAAM,CAClB,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGNV,IAAA,CAACX,YAAY,EACX+I,gBAAgB,CAAEvH,WAAY,CAC9BwH,oBAAoB,CAAEtH,eAAgB,CACtCuH,iBAAiB,CAAErH,YAAa,CAChCsH,WAAW,CAAE3H,QAAS,CACtB4H,eAAe,CAAE1H,YAAa,CAC9B2H,YAAY,CAAEzH,SAAU,CACzB,CAAC,EACE,CAAC,EACF,CAAC,IAAC,EACJ,CAAC,CACO,CAAC,CAEpB,CAEA,cAAe,CAAAb,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}